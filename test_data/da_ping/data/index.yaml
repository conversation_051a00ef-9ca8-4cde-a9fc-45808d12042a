test_data:
  #  test_360_degree_panoram:
  #    description: "全景图测试"
  #    steps:
  #      - action: goto
  #        value: "https://ahohcrm-pre.autohome.com.cn/screen/home?storeId=3425"
  #      - action: click
  #        selector: text="请选择车辆"
  #      - action: click
  #        selector: css=.letter-navigation > span
  #      - action: click
  #        selector: css=span:nth-child(20)
  #      - action: click
  #        selector: text=小米SU7 Ultra 2025款 Ultra
  #      - action: click
  #        selector: xpath="#scroller div"
  #      - action: click
  #        selector: text=内饰VR
  #      - action: click
  #        selector: title=后排
  #      - action: click
  #        selector: css=.closWindowIcon

  test_Car_sales_rankings:
    description: "销量榜单"
    steps:
      - action: goto
        value: "https://ahohcrm-pre.autohome.com.cn/screen/home?storeId=3425"
      - action: assert_text
        value: "销量榜"
        selector: 销量榜
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="轿车"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="5万以下"
      - action: click
        selector: role=textbox[name="能源类型"]
      - action: click
        selector: text="纯电动"
  test_vehicle_energy_consumption_rankings:
    description: "销量榜单"
    steps:
      - action: goto
        value: "https://ahohcrm-pre.autohome.com.cn/screen/home?storeId=3425"
      - action: click
        selector: 能耗榜
      - action: assert_text
        value: "百公里能耗(kWh)"
        selector: 百公里能耗(kWh)
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="轿车"
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="SUV"
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="MPV"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="5万以下"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="5-8万"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="8-15万"
      - action: click
        selector: role=textbox[name="续航季节"]
      - action: click
        selector: text="夏季"
      - action: click
        selector: role=textbox[name="续航季节"]
      - action: click
        selector: text="冬季"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="价格不限"
  test_vehicle_quality_rankings:
    description: "质量榜单"
    steps:
      - action: goto
        value: "https://ahohcrm-pre.autohome.com.cn/screen/home?storeId=3425"
      - action: click
        selector: 质量榜
      - action: assert_text
        value: "百车故障数"
        selector: 百车故障数
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="轿车"
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="SUV"
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="MPV"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="5万以下"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="5-8万"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="8-15万"
      - action: click
        selector: role=textbox[name="能源类型"]
      - action: click
        selector: text="纯电动"
      - action: click
        selector: role=textbox[name="能源类型"]
      - action: click
        selector: text="插电混动"
      - action: click
        selector: role=textbox[name="能源类型"]
      - action: click
        selector: text="增程式"
  test_vehicle_reputation_rankings:
    description: "口碑榜"
    steps:
      - action: goto
        value: "https://ahohcrm-pre.autohome.com.cn/screen/home?storeId=3425"
      - action: click
        selector: 口碑榜
      - action: assert_text
        value: "评分"
        selector: 评分
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="轿车"
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="SUV"
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="MPV"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="5万以下"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="5-8万"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="8-15万"
      - action: click
        selector: role=textbox[name="能源类型"]
      - action: click
        selector: text="纯电动"
      - action: click
        selector: role=textbox[name="能源类型"]
      - action: click
        selector: text="插电混动"
      - action: click
        selector: role=textbox[name="能源类型"]
      - action: click
        selector: text="增程式"
  test_vehicle_resale_value_rankings:
    description: "保值榜"
    steps:
      - action: goto
        value: "https://ahohcrm-pre.autohome.com.cn/screen/home?storeId=3425"
      - action: click
        selector: 保值榜
      - action: assert_text
        value: "保值率"
        selector: 保值率
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="轿车"
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="SUV"
      - action: click
        selector: role=textbox[name="车型选项"]
      - action: click
        selector: text="MPV"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="5万以下"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="5-8万"
      - action: click
        selector: role=textbox[name="价格区间"]
      - action: click
        selector: text="8-15万"
      - action: click
        selector: role=textbox[name="能源类型"]
      - action: click
        selector: text="纯电动"
      - action: click
        selector: role=textbox[name="能源类型"]
      - action: click
        selector: text="插电混动"
      - action: click
        selector: role=textbox[name="能源类型"]
      - action: click
        selector: text="增程式"
