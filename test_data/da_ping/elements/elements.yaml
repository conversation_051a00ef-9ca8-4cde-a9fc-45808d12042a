elements:


  销量榜: "text=销量榜"
  车型选项: "role=textbox, name='车型选项'"
  价格区间: "role=textbox, name='价格区间'"
  能源类型: "role=textbox, name='能源类型'"
  纯电动选项: "role=listitem, has-text='纯电动'"
  能耗榜: role=button[name="能耗榜"]
  百公里能耗(kWh): "text=百公里能耗(kWh)"
  轿车: "role=listitem, has-text='轿车'"
  SUV: "role=listitem, has-text='SUV'"
  MPV: "role=listitem, has-text='MPV'"
  万以下: text="5万以下"
  8万: text="5-8万"
  15万: text="8-15万"
  续航季节: "role=textbox, name='续航季节'"
  夏季: role=listitem[name="夏季"]
  冬季: role=listitem[name="冬季"]
  价格不限: text="价格不限"
  百车故障数: "text=百车故障数"
  质量榜: role=button[name="质量榜"]
  插电混动: "role=listitem, has-text='插电混动'"
  增程式: "role=listitem, has-text='增程式'"
  评分: "text=评分"
  口碑榜: role=button[name="口碑榜"]
  保值率: "text=保值率"
  保值榜: role=button[name="保值榜"]