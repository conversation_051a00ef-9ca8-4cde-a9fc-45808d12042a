steps:
  login: #保留步骤名
    - action: goto
      value: ""
    - action: fill
      selector: "请输入您的用户名"
      value: "15011290391"
    - action: fill
      selector: "请输入您的密码"
      value: "Aa1234qwer-"
    - action: click
      selector: "登录按钮"
  login_steps:
    - action: goto
      value: ""
    - action: fill
      selector: "请输入您的用户名"
      value: "15011290391"
    - action: fill
      selector: "请输入您的密码"
      value: "Aa1234qwer-"
    - action: click
      selector: "登录按钮"
  create_script:
    - action: click
      selector: 剧本文本
    - action: click
      selector: 开始创作
    - action: fill
      selector: 剧本标题
      value: 剧本标题测试
    - action: click
      selector: 剧本时长
    - action: click
      selector: 剧本时
    - action: click
      selector: 剧本分
    - action: wait
      value: 1
    - action: click
      selector: 剧本时长确定
    - action: click
      selector: 关闭第三个话题
    - action: click
      selector: 关闭第二个话题
    - action: click
      selector: 开始时间输入框
    - action: click
      selector: 话题时
    - action: click
      selector: 话题分
    - action: wait
      value: 1
    - action: click
      selector: 话题时间确定
    - action: click
      selector: 案例引用
    - action: click
      selector: 第一个案例
    - action: fill
      selector: 话题名称
      value: "话题名称测试"
    - action: click
      selector: 生成脚本
    - action: wait
      value: 3
    - action: click
      selector: 第一个脚本纳入剧本
    - action: click
      selector: 保存完整剧本
  delete_script:
    - action: click
      selector: delete_button
    - action: click
      selector: 删除剧本取消按钮
    - action: click
      selector: delete_button
    - action: click
      selector: 删除剧本确定按钮
    - action: wait
      value: 1