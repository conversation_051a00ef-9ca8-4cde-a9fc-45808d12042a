steps:
  - action: navigate
    value: "/login"
    description: "打开登录页面"

  - action: fill
    selector: "username_input"
    value: "${username}"
    description: "输入用户名"

  - action: fill
    selector: "password_input"
    value: "${password}"
    description: "输入密码"

  - action: click
    selector: "login_button"
    description: "点击登录按钮"

  - action: wait_for_element_hidden
    selector: "login_button"
    timeout: 10000
    description: "等待登录完成"

  - action: assert_exists
    selector: "welcome_message"
    description: "验证登录成功"

  - action: store_variable
    name: "current_user"
    value: "${username}"
    scope: "global"
    description: "存储当前用户信息"

# 高级用户登录 - 包含记住我选项
advanced_login:
  - action: navigate
    value: "/login"
    description: "打开登录页面"

  - action: fill
    selector: "username_input"
    value: "${username}"
    description: "输入用户名"

  - action: fill
    selector: "password_input"
    value: "${password}"
    description: "输入密码"

  - if: "${remember_me}"
    then:
      - action: click
        selector: "remember_me_checkbox"
        description: "选中记住我"
    else:
      - action: wait
        value: 0.5
        description: "短暂等待"

  - action: click
    selector: "login_button"
    description: "点击登录按钮"

  - action: wait_for_element_hidden
    selector: "login_button"
    timeout: 10000
    description: "等待登录完成"

  - action: assert_exists
    selector: "welcome_message"
    description: "验证登录成功" 