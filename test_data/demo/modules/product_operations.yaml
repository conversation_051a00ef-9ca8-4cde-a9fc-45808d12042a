add_to_cart:
  - action: click
    selector: "product_${product_id}_add_to_cart"
    description: "将产品添加到购物车"

  - action: wait_for_element_visible
    selector: "add_success_message"
    timeout: 5000
    description: "等待添加成功消息显示"

  - action: store_variable
    name: "cart_count"
    value: "${cart_count + 1}"
    scope: "test_case"
    description: "更新购物车数量变量"

# 根据价格范围筛选产品
filter_by_price:
  - action: click
    selector: "price_filter_dropdown"
    description: "打开价格筛选下拉框"

  - action: fill
    selector: "min_price_input"
    value: "${min_price}"
    description: "输入最低价格"

  - action: fill
    selector: "max_price_input"
    value: "${max_price}"
    description: "输入最高价格"

  - action: click
    selector: "apply_price_filter"
    description: "应用价格筛选"

  - action: wait_for_network_idle
    timeout: 5000
    description: "等待筛选结果加载"

  - action: store_element_count
    selector: "product_item"
    name: "filtered_products_count"
    scope: "test_case"
    description: "存储筛选后的产品数量"

# 循环操作多个产品
batch_product_operation:
  - for_each: "${product_ids}"
    as: "product_id"
    do:
      - action: click
        selector: "product_${product_id}_view"
        description: "查看产品详情"

      - action: wait_for_element_visible
        selector: "product_details"
        description: "等待产品详情加载"

      - action: store_text
        selector: "product_price"
        name: "product_${product_id}_price"
        scope: "test_case"
        description: "存储产品价格"

      - if: "${{ ${operation} == 'add_to_cart' }}"
        then:
          - action: click
            selector: "add_to_cart_button"
            description: "添加到购物车"

          - action: wait_for_element_visible
            selector: "add_success_message"
            description: "等待添加成功"
        else:
          - action: click
            selector: "add_to_wishlist_button"
            description: "添加到收藏"

          - action: wait_for_element_visible
            selector: "wishlist_success_message"
            description: "等待收藏成功"

      - action: click
        selector: "back_to_products"
        description: "返回产品列表" 