elements:
  首页-登录用户姓名: div.ai-card__role>span.name
  首页-待办列表-全部: div.todo-today__title>label.position>span.position__txt
  下订信息-保存按钮: div.orderConfirm-header-right
  下订信息-基础信息-展开: div.van-cell__value
  下订信息-上牌城市: input[name="registerCityName"]
  下订信息-上牌费用输入框: input[name="registerAmount"]
  下订信息-付款凭证输入框: div.uploader-box.payPic-box[data-v-acd976af] input.van-uploader__input
#  xpath=//input[@name="payPic"]/parent::div
  下订信息-付款日期-选择日期-确认按钮: xpath=//div[text()="选择付款时间"]/following-sibling::button
  下订信息-付款日期输入框:
    xpath=//*[@id="scrolltarget"]/div/div[2]/div/section/form/div[4]/div[2]/div/input
  下订信息-企业代码: input[name="socialCode"]
  下订信息-身份证号: input[name="identityCard"]
  下订信息-公户: div.select-buyertype-main>div>div:nth-child(2)
  下订信息-个户: div.select-buyertype-main>div>div:nth-child(1)
  下订信息-其他项: xpath=//span[text()="其他"]
  下订信息-合同凭证输入框: div.uploader-box.contractPic-box[data-v-acd976af] div.van-uploader__upload input.van-uploader__input
#  xpath=//input[@name="contractPic"]/parent::div
  下订信息-合同日期-选择日期-确认按钮: xpath=//div[text()="选择合同日期"]/following-sibling::button
  下订信息-合同日期输入框:
    xpath=//*[@id="scrolltarget"]/div/div[2]/div/section/form/div[3]/div[2]/div/input
  下订信息-合同类型-线上合同: xpath=//div[text()="线上合同"]
  下订信息-开票价格输入框: input[name="invoiceAmount"]
  下订信息-当日转化情况-证明图片: xpath=//input[@name="picList"]/..
  下订信息-当日转化情况类型-自然进店: xpath=//div[@class="radio-item-text"][text()="自然进店"]
  下订信息-录音文件可选最早的开始日期: div.van-calendar__body>div:nth-child(1)>div>div:nth-child(1)
  下订信息-录音文件日期范围: section.filter-arrow
  下订信息-录音文件日期面板: div.van-calendar__body
  下订信息-录音文件日期面板中的今天: div.van-calendar__body>div:last-child>div.van-calendar__days>div:nth-child(1)
  下订信息-录音文件日期面板年月子标题: div.van-calendar__header>div.van-calendar__header-subtitle
  下订信息-录音文件日期面板按钮-确定: section.policy-dropdown-btn>section.confirm
  下订信息-录音文件日期面板-2024年3月1日: //div[@class='van-calendar__month'][./div[@class='van-calendar__month-title' and contains(text(),'2024年3月')]]//div[@class='van-calendar__day'][1]
  下订信息-录音文件日期面板-2024年7月1日: //div[@class='van-calendar__month'][./div[@class='van-calendar__month-title' and contains(text(),'2024年7月')]]//div[@class='van-calendar__day'][1]
  下订信息-录音文件输入框: xpath=//input[@name="soundList"]/parent::div
  下订信息-提交审核按钮: xpath=//section[@class="personalForm-btn"]/span[2]
  下订信息-提车日期-选择日期-确认按钮: button.van-picker__confirm
  下订信息-提车日期输入框: input[placeholder="请选择提车日期"]
  下订信息-月供输入框: input[name="monthlyPayment"]
  下订信息-购车总价输入框:
    xpath=//*[@id="scrolltarget"]/div/div[2]/div/section/form/div[1]/div[2]/div/input
  下订信息-购车类型-下一步按钮: div.operation-btn-big
  下订信息-贷款期数输入框: div.van-radio-group:nth-child(1)>div:nth-child(1)
  下订信息-贷款额度输入框: input[name="loanAmount"]
  下订信息-身份证号输入框: input[name="identityCard"]
  下订信息-车型输入框: xpath=//input[@placeholder="请选择车型"]/parent::div
  下订信息-选择录音-确定按钮: section.audios-wrapper>div>div.form-btn>section
  下订信息-选择录音-第一条录音: section.audio-player-list>div:nth-child(1)>div>span
  下订信息-选择车系: div.spec-name-list>div:nth-child(1)
#  app>div:nth-child(7)>div>div.order-specSelect-content>div>div.spec-name-list>div:nth-child(1)
  下订成功-返回: xpath=//*[@id="app"]/div/div/div/section/section/div/div/section[1]/div
  任务填跟进-下订车系选择第一个: div.series-name-left
  任务填跟进页-下订车系: span.no-selected-audios
  任务填跟进页-二级战败因素弹框-确认按钮: div.commonFileCheck-popup-btn_bgc
  任务填跟进页-工牌录音-无录音: xpath=//div[text()="无录音"]
  任务填跟进页-工牌录音-无录音-填写原因-工牌没电: xpath=//div[@class="radio-item-text"][text()="工牌没电"]
  任务填跟进页-持续跟进任务-跟进原因: span.follow-reason_value
  任务填跟进页-提交跟进记录按钮: div.task-form-btn
  任务填跟进页-见面开关: div[role="switch"][aria-checked="false"]
  任务填跟进页-跟进结果-下订: xpath=//div[text()="下订"]
  任务填跟进页-跟进结果-再联系: xpath=//div[@class="radio-item-text" and text()="再联系"]
  任务填跟进页-跟进结果-再联系-下次联系时间半屏页-确认按钮: button.van-picker__confirm
  任务填跟进页-跟进结果-再联系-下次联系时间输入框: form>div[trigger="onChange"]
  任务填跟进页-跟进结果-战败: xpath=//div[@class="radio-item-text" and text()="战败"]
  任务填跟进页-跟进结果-战败-一级战败因素选项: xpath=//div[@class="radio-item-text" and text()="至二级因素选择"]
  任务填跟进页-跟进结果-战败-二级战败因素弹框-第一个选项: div.commonFileCheck-popup-content-scroll>div:nth-child(1)
  任务填跟进页-跟进结果-战败-二级战败因素输入框: xpath=//input[contains(@placeholder, '因素')]
  任务填跟进页面标题: div.van-popup--full>div>section>div>div>section.nav-title
  任务详情-推送按钮: div.customerDetail-foot>div:nth-child(2)>img
  全屏页页面标题: section.nav-title
  全部订单: div.fun-button__item-order
  切换登录方式: section.login-cut
  创建用户: div.adduser-btn
  到店类型-到门店:
    xpath=//*[@id="app"]/div[2]/section[1]/section[2]/div[1]/form/div[5]/div[1]/section/div/div[2]/div/div/section/section/section[1]
  到店诉求-现场活动:
    xpath=//*[@id="app"]/div[2]/section[1]/section[2]/div[1]/form/div[5]/div[2]/section/div/div[2]/div/div/section/section/section[2]
  到店诉求-试乘试驾: xpath=//section[text()="试乘试驾"]
  发票信息-返回上一页: div.go-back-icon
  发票信息页-上传发票: div[data-v-c75feaa3] input.van-uploader__input[type='file'][accept='image/*'][multiple]
#  div.invoice-info-upload-wrap
  发票信息页-上传发票文案: p.invoice-upload-txt
  发票信息页-确认发票二次确认弹窗取消按钮: button.van-dialog__cancel
  发票信息页-确认发票二次确认弹窗文案: div.invoice-dialog-title
  发票信息页-确认发票二次确认弹窗确认按钮: button.van-dialog__confirm
  发票信息页-确认发票按钮: div.operation-btn-dark
  发票信息页-补充材料上传: div.van-uploader__upload
  发票已上传页-发票已上传文案: div.home-content>section:nth-child(1)>div.invoice-info-title
  发票已上传页-发票已上传请校验真实性文案: div.home-content>section:nth-child(1)>div.invoice-info-desc-success
  发票已上传页-已确认按钮: div.invoice-footer-operation>div
  同意协议: xpath=//*[@class="van-checkbox__icon van-checkbox__icon--round"]
  客户信息-对客作业-全部tab-最新记录-备注:
    div.detailFollow-conter>div:nth-child(2)>div>div.detailFollow-middle>div.record-item>div.records-content-call>span.record-call-value
  客户信息-对客作业-全部tab-最新记录-跟进内容:
    div.detailFollow-conter>div:nth-child(2)>div>div.detailFollow-middle>div.record-item>div.record-type>div:nth-child(3)>span:nth-child(2)
  客户信息-对客作业-全部tab-最新记录-跟进卡片: div.detailFollow-conter-item>div:nth-child(1)
  客户信息-对客作业-全部tab-最新记录-跟进方式:
    div.detailFollow-conter>div:nth-child(2)>div>div.detailFollow-middle>div.record-item>div.record-type>div:nth-child(2)>span:nth-child(2)
  客户信息tab-任务信息: div[role="tablist"]>div:nth-child(2)
  客户信息tab-对客作业: div[role="tablist"]>div:nth-child(3)
  客户写备注-提交按钮: div.task-form-btn
  客户写备注-证明截图: ".uploader-box.ScreenshotProof-box input[type='file']"
  客户写备注-跟进内容-有到店意向: xpath=//div[text()="有到店意向"]
  客户写备注-跟进内容-未联系上: xpath=//div[text()="未联系上"]
  客户写备注-跟进内容输入框输入进度: div.van-field__word-limit>span.van-field__word-num
  客户写备注-跟进方式-企微: xpath=//div[text()="企微"]
  客户写备注-跟进方式-微信: xpath=//div[text()="微信"]
  客户写备注-跟进方式-电话: xpath=//div[text()="电话"]
  客户写备注-跟进方式-见面: xpath=//div[text()="见面"]
  客户列表-搜索: xpath=//*[@id="app"]/div/div/div[1]/section/section/div/div/section[2]/div
  客户列表-第一个客户卡片: div.van-list>div:nth-child(1)
  客户列表-第一个客户卡片中右上角线索状态: div.van-list>div:nth-child(1)
  客户列表_执行搜索: xpath=//*[@class="van-badge__wrapper van-icon van-icon-search"]
  客户列表搜索页后退箭头: img.search-left-icons
  客户列表查询条件输入框: xpath=//*[@id="app"]/div/div/div/div/div[1]/div/div/div/div/div[2]/div/input
  客户列表查询结果第一条写备注按钮:
    div.van-list>div:nth-child(1)>div.customer-card>div.footContainer>div:nth-child(1)
  客户列表查询结果第一条填跟进按钮:
    xpath=//*[@id="app"]/div/div/div/div/div[2]/div/div/div[2]/div[1]/div/div[2]/div[4]
  客户详情-线索id: div.detailcard-cont-item-bottom>div:nth-child(2)>div.detailcard-cont-value
  客户姓名: input[name="customName"]
  客户详情页面点击填跟进: xpath=//*[@class="follow-text"]
  客户跟进详情页-任务类型: div.work-content>div:nth-child(1)>span.record-call-value
  客户跟进详情页-后退箭头: div.go-back-icon
  客户跟进详情页-购车周期: div.work-content>div:nth-child(5)>span.record-call-value
  客户跟进详情页-跟进内容: div.work-content>div:nth-child(7)>span.record-call-value
  客户跟进详情页-跟进方式: div.work-content>div:nth-child(6)>span.record-call-value
  工作-签到记录: div.van-pull-refresh__track>div:nth-child(2)>div:nth-child(2)
  底部菜单-客户: xpath=//*[@id="app"]/div/div/div[2]/div[2]
  底部菜单-工作: div.van-tabbar>:nth-child(1)>div.van-tabbar-item__text
  待办-全部: div.todo-today__title>label.position>span.position__txt
  待办列表-任务类型-待接待: div[role="tablist"]>div:nth-child(6)
  待办列表-任务类型-预约确认: div[role="tablist"]>div:nth-child(5)
  待清洗任务填跟进页-其他备注-文本框: textarea.van-field__control
  待清洗任务填跟进页-到店时间: xpath=//input[@class="van-field__control"][@placeholder="请选择到店时间"]
  待清洗任务填跟进页-到店时间-确认按钮: button.van-picker__confirm
  待清洗任务填跟进页-到店类型-到门店: xpath=//div[@class="radio-item-text"][text()="到门店"]
  待清洗任务填跟进页-到店诉求-全息体验:
    div.FileFollowCarCheck-wrap>div>div>div.van-cell__value>div>div>div>div:nth-child(3)>span
  待清洗任务填跟进页-到店诉求-现场活动:
    div.FileFollowCarCheck-wrap>div>div>div.van-cell__value>div>div>div>div:nth-child(2)>span
  待清洗任务填跟进页-清洗结果-无效: div.van-radio-group>div:nth-child(3)
  待清洗任务填跟进页-清洗结果-无效-无效原因选择框: input[placeholder="请选择无效原因"]
  待清洗任务填跟进页-清洗结果-有效: div.van-radio-group>div:nth-child(1)
  待清洗任务填跟进页-清洗结果-有效-用户诉求-管家回电:
    form.van-form>div:nth-child(5)>div>div.van-cell__value>div>div>div>div:nth-child(1)
  待清洗任务填跟进页-用户诉求-预计到店: text=预计到店
  #    form.van-form>div:nth-child(5)>div>div.van-cell__value>div>div>div>div:nth-child(2)
  待清洗任务填跟进页-管家回电-咨询内容-买车议价: form.van-form>div:nth-child(6)>div>div:nth-child(2)>div>div>div>div:nth-child(1)
  待清洗任务填跟进页-管家回电-回电时间弹窗-确认按钮: div.van-picker__toolbar>button.van-picker__confirm
  待清洗任务填跟进页-管家回电-回电时间输入框: xpath=//input[@placeholder='请选择回电时间']
  手机号: id=customMobile
  推送-添加车型按钮: //section[@class="filter-btn confirm"]
  推送-选择推送车型半屏页-删除按钮: text=删除
#    section.confirm
  推送-确定选择按钮: div.submit-btn-box>section
  推送-选择推送车型半屏页: section.push-lead-content>section.popup-header #点击推送按钮出现的半屏页
  推送-确认推送: div.confirm-btn
  推送-选择车型页搜索框搜索: section.car-selection .van-search.reset-search .van-field.van-search__field.reset-search .van-field__left-icon
  推送-选择筛选的第一个车型: section.search-car-group>section:nth-child(1)
  推送-选择所属经销商: xpath=//input[@placeholder="请选择经销商"]
  推送-选择推送车型: xpath=//*[@class="car-name"]
  推送-选择车型输入框: input[placeholder="请输入车型"]
  推送-选择车型页标题: section.popup-header
  推送-选择随机经销商: section.dealer-list-box
  推送经销商弹框-取消按钮: div.footer-btn-box>:nth-child(1)
  推送经销商弹框-确定按钮: span.confirm-style
  搜索框-返回: div.search-header>img
  新增用户-备注: id=van-field-6-label
  新增用户按钮: section.submit-btn
  是否已到店: xpath=//*[@class="swith-btn"]
  清洗结果无效-无效原因半屏页-已购车: xpath=//span[text()="已购车"]
  清洗结果无效-无效原因半屏页-确定按钮: div.commonFileCheck-popup-btn_bgc
  清洗结果无效-无效原因半屏页标题: section.popup-header
  登录密码: id=login-passwordInput
  登录按钮: xpath=//button[@type="button"]
  登录账号: id=login-phoneInput
  线索详情-返回上一页: div.go-back-icon
  线索详情-任务信息-第一个主任务的名称: .instore-info-main > .instore-info-item-card:first-child .type-label
  线索详情页-任务信息-第一个主任务的状态: span.type-status  #.instore-info-main > .instore-info-item-card:first-child .type-status
  线索详情页-任务信息-第一个子任务的状态: div.title-left>div.tag
  线索详情页-任务信息-第一个子任务取消按钮: div.bottom-btn>span.btn-cancel
  线索详情页-任务信息-第一个子任务开始按钮: div.bottom-btn>span.btn-primary
  #取消看舱或是取消试驾的半屏页
  线索详情页-任务信息-第一个子任务取消原因-客户表示不需要: section.search-car-group>section:nth-child(1)
  cancel_reason_1: text=客户表示不需要 # 选择取消原因 - 客户表示不需要
  cancel_reason_confirm: //section[@class="common-btn submit-btn"]
  线索详情页-任务信息-第一个子任务取消原因-半屏页标题: section.popup-header
  线索详情页-任务信息-第一个子任务取消原因-半屏页确定按钮: //section[@class="common-btn submit-btn"]
  线索详情页-待填跟进主任务看舱子任务取消原因-客户表示不需要: section.search-car-group>section:nth-child(1)
  线索详情页-待填跟进主任务看舱子任务取消原因半屏页标题: section.popup-header
  线索详情页-待填跟进主任务看舱子任务取消原因页确定按钮: section.common-btn
  线索详情页-待填跟进主任务看舱子任务状态: div.title-left>div.tag
  线索详情页-待填跟进主任务看舱子任务结束按钮: div.bottom-btn>span.btn-left-border
  线索详情页-待填跟进主任务看舱子任务车型半屏页-确定按钮: section.car-selection>section:nth-child(3)
  线索详情页-待填跟进主任务看舱子任务车型半屏页第一个车型: section.car-selection>section:nth-child(2)>section:nth-child(1)>h1.car-info
  线索详情页面-任务tab-第一个任务点击填跟进: span.follow-text
  ##试驾任务的线索详情内容
  线索详情页-任务tab-第一个主任务填跟进: "span.follow-text" # 填跟进
  #线索详情页-任务tab-第一个主任务状态: "span.type-status" # 服务中
  user_appeal: "span.label" # 用户诉求
  线索详情页-任务tab-第一个主任务的用户诉求内容: "span.desc" # 用户诉求 Value
  pre_time: "span.label" # 预约时间
  pre_time_value: "span.desc" # 预约时间 Value
  线索详情页-任务tab-第一个子任务的名称: "h4" # 门店试驾
  线索详情页-任务tab-最底下的任务名称标签: div.instore-info>div.instore-info-main:last-child>div.instore-info-item-card>div.task-type>div.title-content>span
  线索详情页-任务tab-最底下的任务状态: div.instore-info>div.instore-info-main:last-child>div.instore-info-item-card>div.task-type>span.type-status
  线索详情页- 任务tab-最底下的任务预约时间标签: div.instore-info>div.instore-info-main:last-child>div.instore-info-item-card>div:nth-child(2)>span.label
  线索详情页- 任务tab-最底下的任务完成时间标签: div.instore-info>div.instore-info-main:last-child>div.instore-info-item-card>div:nth-child(3)>span.label
  add_service: "span[data-v-7d609ef8]" # 新增服务事项
  phone_button: "div.fillInWhiteBtnForCustomer" # 打电话
  线索详情页面标题-线索状态: section.nav-title
  线索详情页面标题-线索状态2: "#app > div.app-wrapper > div > div > section > section > div >div>section.nav-title"

  订单列表-搜索: div.search-icon
  订单列表-搜索输入框: xpath=//*[@id="app"]/div/div/div/div/div[1]/div/div/div/div/div[2]/div/input
  订单列表-返回:
    section.home-wrap>section.nav-bar-box>div.nav-bar-content-wraper>div.nav-bar-content>section.nav-left
  订单列表页面标题:
    section.home-wrap>section.nav-bar-box>div.nav-bar-content-wraper>div.nav-bar-content>section.nav-title
  订单列表第一个卡片: div.listcont
  订单卡片-上传发票/查看发票按钮:
    xpath=id("app")/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[4]/div[2]/div[1]
  订单卡片-填写信息按钮: div#app > div > div > div > div > div:nth-of-type(2) > div > div >
    div:nth-of-type(2) > div > div > div > div:nth-of-type(4) > div:nth-of-type(2)
    > div:nth-of-type(3)
  订单列表第一个卡片-三个点: .orderListcard > .listcard:first-child .van-popover__wrapper
  订单卡片-取消下订选项: div.van-popover__action
  取消下订确认弹窗确认取消文案: div.van-dialog__content>div
  取消下订确认弹窗转跟进任务文案:  div.van-dialog__content>p
  取消下订确认弹窗-确认按钮: div[class="van-hairline--top van-dialog__footer"]>button:nth-child(2)
  取消下订确认弹窗-取消按钮: div[class="van-hairline--top van-dialog__footer"]>button:nth-child(1)

  订单卡片第一个-发票状态:
    div.orderListcard>div:nth-child(1)>div.listcont>div.listcard-cont>div:nth-child(5)>div.listcard-cont-value
  订单卡片第一个订单状态: span.common_span #左上角待审核、审核中等
  试驾车系选择框: section[placeholder="请选择试驾车系"]
  购车周期-存疑: xpath=//div[@class="radio-item-text"][text()="存疑"]
  选择下订车型页-确定按钮: div.operation-btn-big
  选择下订车型页-第一个下订车型: div.series-spec-list>div>div:nth-child(2)
  选择试驾半屏页-确定按钮: div[id="app"]>div:nth-child(5)>section>section:nth-child(3)
  选择试驾半屏页-第一个试驾车: section.search-car-item
  选择试驾半屏页title: section.popup-header
  选择车系页-确定按钮: div.operation-btn-big
  选择车系页标题: span.header-title
  验收测试文本: xpath=//*[@id="login"]/section[2]/span

  订单卡片-确认函按钮: xpath=//div[@class="common-btn submit-debounce brandPolicyType-confirm-btn"]
  确认函签署-页面标题: div.non-keyBrand-Confirm-header-title
  确认函签署填写信息页-身份证输入框: xpath=//input[@name='identityCard']
  确认函签署填写信息页-购买车型选择框: text=购买车型
  确认函签署填写信息页-选择车型页标题: span.header-title
  确认函签署填写信息页-选择车型页第一项: div.spec-name-list>div:nth-child(1)
  确认函签署填写信息页-选择车型的半屏页-确定按钮: div.operation-btn-big
  确认函签署填写信息页-指导价输入框: input[name='specPrice']
  确认函签署填写信息页-发票价格输入框: input[name='invoiceAmount']
  确认函签署填写信息页-合同日期输入框: input[name='contractDate']
  确认函签署填写信息页-合同日期半屏页标题: div[class='van-picker__title van-ellipsis']
  确认函签署填写信息页-合同日期半屏页-确任按钮: button.van-picker__confirm
  确认函签署填写信息页-合同日期半屏页-底部确定按钮: div.non-keyBrand-Confirm-bottom-btn-text
  确认函预览-标题: div.h2
  确认函预览-客户名称: div.buy-car-info>p:nth-child(2)>span:nth-child(2)
  确认函预览-手机号: div.buy-car-info>p:nth-child(3)>span:nth-child(2)
  确认函预览-身份证号: div.buy-car-info>p:nth-child(4)>span:nth-child(2)
  确认函预览-购买车辆品牌: div.buy-car-info>p:nth-child(5)>span:nth-child(2)
  确认函预览-购买车辆MSRP(官方指导价): div.buy-car-info>p:nth-child(8)>span:nth-child(2)
  确认函预览-购买车辆价格(发票价): div.buy-car-info>p:nth-child(9)>span:nth-child(2)
  确认函预览-分享客户微信按钮: div.confirmation-button>button:nth-child(2)
  确认函预览-短信按钮: div.confirmation-button>button:nth-child(1)
  确认函预览-邀请信息-复制文本: text=复制文本


#订单详情页
  订单详情-确认函tab: //div[@role="tablist"]/div[@tabindex="0"]/span
  订单详情-确认函客户姓名: section.confirm-view>div.order-container>section:nth-child(1)>span.filed-cell-content
  订单详情-确认函客户手机号: section.confirm-view>div.order-container>section:nth-child(2)>span.filed-cell-content
  订单详情-确认函身份证号: section.confirm-view>div.order-container>section:nth-child(3)>span.filed-cell-content
  订单详情-确认函购买车型: section.confirm-view>div.order-container>section:nth-child(4)>span.filed-cell-content
  订单详情-确认函厂商指导价: section.confirm-view>div.order-container>section:nth-child(5)>span.filed-cell-label
  订单详情-确认函发票价: section.confirm-view>div.order-container>section:nth-child(6)>span.filed-cell-label
  订单详情-确认函合同日期: section.confirm-view>div.order-container>section:nth-child(7)>span.filed-cell-label
  订单详情-有确认函的订单信息tab: //div[@role="tablist"]/div[@tabindex="-1"]/span
  订单详情-取消下订按钮: div.fillInWhiteBtnForCancel

  订单详情-基础信息-上牌城市: section.echo-container>section:nth-child(1)>section:nth-child(2)>span.filed-cell-content
  订单详情-基础信息-开票价格: section.echo-container>section:nth-child(1)>section:nth-child(3)>span.filed-cell-content
  订单详情-基础信息-店内上牌: section.echo-container>section:nth-child(1)>section:nth-child(4)>span.filed-cell-content
  订单详情-基础信息-上牌费用: section.echo-container>section:nth-child(1)>section:nth-child(5)>span.filed-cell-content
  订单详情-基础信息-店内投保商业保险: section.echo-container>section:nth-child(1)>section:nth-child(6)>span.filed-cell-content
  订单详情-基础信息-是否贷款: section.echo-container>section:nth-child(1)>section:nth-child(7)>span.filed-cell-content
  订单详情-基础信息-贷款额度: section.echo-container>section:nth-child(1)>section:nth-child(8)>span.filed-cell-content
  订单详情-基础信息-贷款期数: section.echo-container>section:nth-child(1)>section:nth-child(9)>span.filed-cell-content
  订单详情-基础信息-月供金额: section.echo-container>section:nth-child(1)>section:nth-child(10)>span.filed-cell-content
  订单详情-其他-购车总价: section.echo-container>section:nth-child(3)>section:nth-child(2)>span.filed-cell-content
  订单详情-其他-提车日期: section.echo-container>section:nth-child(3)>section:nth-child(3)>span.filed-cell-content
  订单详情-其他-合同日期: section.echo-container>section:nth-child(3)>section:nth-child(4)>span.filed-cell-content
  订单详情-其他-付款日期项:  section.echo-container>section:nth-child(3)>section:nth-child(5)>span.filed-cell-label
  订单详情-其他-合同凭证: div.echo-module-image>section:nth-child(1)>div:nth-child(2)
  订单详情-其他-身份证号: div.echo-module-image>section:nth-child(3)>div:nth-child(2)
  订单详情-其他凭证:  div.echo-module-image>section:nth-child(4)>div:nth-child(2)