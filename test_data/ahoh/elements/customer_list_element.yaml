elements:
  客户列表非待清洗&非下订线索第一个卡片的更多按钮: span.van-popover__wrapper>div #客户卡片第一个按钮
  客户列表非待清洗&非下订线索第一个卡片的更多选项中的推送按钮: div.van-popover__content>div>div.van-popover__action-text
  客户列表第一个卡片待清洗&下订线索的写备注按钮: .van-list .listcard:first-child .footContainer .fillInWhiteBtn:nth-child(1) #客户卡片第一个按钮
  客户列表第一个卡片的线索状态: .van-list .listcard:first-child .status  # 定位线索状态
  # 第一个用户名
  客户列表第一个卡片的线索名称: .van-list .listcard:first-child .username  # 定位用户名
  # 第一个用户等级
  客户列表第一个卡片的线索等级: .van-list .listcard:first-child .level  # 定位用户等级
  # 第一个客户卡片
  客户列表第一个卡片: .van-list .listcard:first-child  # 定位第一个客户卡片
  客户列表待清洗&预约待接待线索第一个卡片的线索来源: div.van-list>div:nth-child(1)>div.customer-card>div.customer-info>div.header-container>div.listcard-item>:nth-child(2)>div.value-oneline # 定位单行显示的值
  客户列表第一个卡片的意向车系: div.value-diff
  # 第一个客户卡片内部的客户信息容器
  first_customer_info: ".van-list .listcard:first-child .customer-card .customer-info" # 定位客户信息容器
  # 第一个客户信息头部容器
  first_header_container: ".van-list .listcard:first-child .header-container" # 定位头部信息容器
  # 第一个客户姓名和等级容器
  first_user_name_level: ".van-list .listcard:first-child .user-name-level"  # 定位包含用户名和等级的容器
  # 第一个客户电话
  first_listcard_phone: ".van-list .listcard:first-child .listcard-phone"  # 定位客户电话号码
  # 第一个列表项（来源、线索进店、到店时间等）
  first_listcard_item: ".van-list .listcard:first-child .listcard-item" # 定位列表项
  # 第一个列表项名称 (例如: 来源, 线索进店)
  first_listcard_item_name: ".van-list .listcard:first-child .listcard-item__name"  # 定位列表项的名称标签
  # 第一个收藏按钮
  first_favorite_button: ".van-list .listcard:first-child .footContainer .fillInWhiteBtn:nth-child(2)" # 定位加收藏按钮

  # 第一个打电话按钮
  first_call_button: ".van-list .listcard:first-child .footContainer .fillInWhiteBtn:nth-child(3)" # 定位打电话按钮

  # 第一个填写跟进按钮
  first_follow_up_button: ".van-list .listcard:first-child .footContainer .fillInWhiteBtn.blueThemeColor" # 定位填写跟进按钮
  # 第一个更多信息按钮
  first_instore_info_title: ".van-list .listcard:first-child .instore-info-title" # 定位更多信息按钮

