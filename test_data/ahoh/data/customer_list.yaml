test_data:

  test_push_car_models_in_customer_list:
    description: 客户列表预约待接待的线索推送功能,客户卡片线索来源和意向车型验证
    steps:
#      - action: execute_python
#        value:  files/ahoh/createLeadsInterface.py
      - use_module: login_steps
      - use_module: create_store_unvisited_leads_steps
      - action: 刷新
      - action: 点击
        selector: 底部菜单-客户
      - action: 点击
        selector: 客户列表-搜索
      - action: 输入
        selector: 客户列表查询条件输入框
        value: ${phone_num}
      - action: 点击
        selector: 客户列表_执行搜索
      - action: 等待
        value: 1
      - action: 验证文本
        selector: 客户列表第一个卡片待清洗&下订线索的写备注按钮
        value: 写备注
      - action: 验证
        selector: 客户列表待清洗&预约待接待线索第一个卡片的线索来源
        value:  ${ahoh_lead_source}
      - action: 点击
        selector: 客户列表查询结果第一条填跟进按钮
      - action: 等待
        value: 2
      - action: 存储文本
        selector: 客户详情-线索id
        variable_name: lead_id
      - action: 点击
        selector: 客户详情页面点击填跟进
      - action: 验证
        selector: 任务填跟进页面标题
        value: 待清洗任务
      - action: 点击
        selector: 待清洗任务填跟进页-清洗结果-有效
      - action: 点击
        selector: 购车周期-存疑
      - action: 点击
        selector: 待清洗任务填跟进页-用户诉求-预计到店
      - action: 点击
        selector: 待清洗任务填跟进页-到店诉求-现场活动
      - action: 点击
        selector: 待清洗任务填跟进页-到店类型-到门店
      - action: 点击
        selector: 待清洗任务填跟进页-到店时间
      - action: 点击
        selector: 待清洗任务填跟进页-到店时间-确认按钮
      - action: 点击
        selector: 任务填跟进页-提交跟进记录按钮
      - action: 刷新
      - action: 点击
        selector: 底部菜单-客户
      - action: 点击
        selector: 客户列表-搜索
      - action: 输入
        selector: 客户列表查询条件输入框
        value: ${phone_num}
      - action: 点击
        selector: 客户列表_执行搜索
      - action: 验证
        selector: 客户列表非待清洗&非下订线索第一个卡片的更多按钮
        value: 更多
      - action: 点击
        selector: 客户列表非待清洗&非下订线索第一个卡片的更多按钮
      - action: 验证
        selector: 客户列表非待清洗&非下订线索第一个卡片的更多选项中的推送按钮
        value: 推送
      - action: 点击
        selector: 客户列表非待清洗&非下订线索第一个卡片的更多选项中的推送按钮
      - action: 验证
        selector: 推送-选择推送车型半屏页
        value:  选择推送车型
      - action: 验证
        selector: 推送-选择推送车型半屏页-删除按钮
        value:  删除
      - action: 验证
        selector: 推送-添加车型按钮
        value:  添加车型
      - action: 等待
        value: 2
      - action: 点击
        selector: 推送-添加车型按钮
      - action: 验证
        selector: 推送-选择车型页标题
        value: 选择车型
      - action: 输入
        selector: 推送-选择车型输入框
        value: 新海狮X30L
      - action: 点击
        selector: 推送-选择车型页搜索框搜索
      - action: 点击
        selector: 推送-选择筛选的第一个车型
      - action: 点击
        selector: 推送-确定选择按钮
      - action: 点击
        selector: 推送-选择推送车型
      - action: 点击
        selector: 推送-选择所属经销商
      - action: 点击
        selector: 推送-选择随机经销商
      - action: 点击
        selector: 推送-确认推送
      - action: 点击
        selector: 推送经销商弹框-确定按钮
      - action: 验证文本
        selector: 客户列表第一个卡片的意向车系
        value: ${ahoh_lead_seriesName}
      - action: 验证文本
        selector: 客户列表待清洗&预约待接待线索第一个卡片的线索来源
        value: ${ahoh_lead_source}
      - action: 点击
        selector: 客户列表查询结果第一条填跟进按钮
      - action: 等待
        value:  1
      - action: 点击
        selector: 客户详情页面点击填跟进
      - action: 点击
        selector: 购车周期-存疑
      - action: 点击
        selector: 任务填跟进页-跟进结果-战败
      - action: 点击
        selector: 任务填跟进页-跟进结果-战败-一级战败因素选项
      - action: 点击
        selector: 任务填跟进页-跟进结果-战败-二级战败因素输入框
      - action: 点击
        selector: 任务填跟进页-跟进结果-战败-二级战败因素弹框-第一个选项
      - action: 点击
        selector: 任务填跟进页-二级战败因素弹框-确认按钮
      - action: 点击
        selector: 任务填跟进页-提交跟进记录按钮
      - action: 等待
        value: 1
      - action: 验证
        selector: 线索详情页面标题-线索状态 #更换验证元素定位
        value: 战败
      - action: 刷新
        selector: ''
      - action: 验证
        selector: 底部菜单-工作
        value: 工作

  test_customer_cleaning_task_info:
    description: 客户列表 服务中的清洗任务的信息
    steps:
      - use_module: login_steps
      - use_module: create_store_unvisited_leads_steps
      - action: 刷新
      - action: 点击
        selector: 底部菜单-客户
      - action: 点击
        selector: 客户列表-搜索
      - action: 输入
        selector: 客户列表查询条件输入框
        value: ${phone_num}
      - action: 点击
        selector: 客户列表_执行搜索
      - action: 等待
        value: 1
      - action: 点击
        selector: 客户列表-第一个客户卡片
      - action: 硬断言
        selector: 线索详情-任务信息-第一个主任务的名称
        value:  待清洗
      - action: 硬断言
        selector: 客户详情页面点击填跟进
        value: 填跟进
      - action: 硬断言
        selector: 线索详情页-任务信息-第一个主任务的状态
        value: 服务中
      - action: 点击
        selector: 客户详情页面点击填跟进
      - action: 硬断言
        selector: 任务填跟进页面标题
        value: 待清洗任务
      - action: 点击
        selector: 待清洗任务填跟进页-清洗结果-无效
      - action: 点击
        selector: 待清洗任务填跟进页-清洗结果-无效-无效原因选择框
      - action: 硬断言
        selector: 清洗结果无效-无效原因半屏页标题
        value: 无效原因
      - action: 点击
        selector: 清洗结果无效-无效原因半屏页-已购车
      - action: 点击
        selector: 清洗结果无效-无效原因半屏页-确定按钮
      - action: 点击
        selector: 任务填跟进页-提交跟进记录按钮
      - action: 硬断言
        selector: 线索详情页面标题-线索状态2
        value: 无效
      - action: 刷新
      - action: 等待
        value: 1
      - action: 硬断言
        selector: 底部菜单-工作
        value: 工作

  test_customer_cleaned_task_info:
    description: 客户列表 已完成的清洗任务的信息(清洗任务生成管家回电任务)--已调通
    steps:
      - use_module: login_steps
      - use_module: create_store_unvisited_leads_steps
      - action: 刷新
      - action: 点击
        selector: 底部菜单-客户
      - action: 点击
        selector: 客户列表-搜索
      - action: 输入
        selector: 客户列表查询条件输入框
        value: ${phone_num}
      - action: 点击
        selector: 客户列表_执行搜索
      - action: 等待
        value: 1
      - action: 点击
        selector: 客户列表-第一个客户卡片
      - action: 硬断言
        selector: 线索详情-任务信息-第一个主任务的名称
        value:  待清洗
      - action: 点击
        selector: 客户详情页面点击填跟进
      - action: 验证
        selector: 任务填跟进页面标题
        value: 待清洗任务
      - action: 点击
        selector: 待清洗任务填跟进页-清洗结果-有效
      - action: 点击
        selector: 购车周期-存疑
      - action: 点击
        selector: 待清洗任务填跟进页-清洗结果-有效-用户诉求-管家回电
      - action: 点击
        selector: 待清洗任务填跟进页-管家回电-咨询内容-买车议价
      - action: 点击
        selector: 待清洗任务填跟进页-管家回电-回电时间输入框
      - action: 点击
        selector: 待清洗任务填跟进页-管家回电-回电时间弹窗-确认按钮
      - action: 点击
        selector: 任务填跟进页-提交跟进记录按钮
      - action: 验证
        selector: 线索详情页面标题-线索状态2
        value: 咨询待预约
      - action: 硬断言
        selector: 线索详情页-任务tab-最底下的任务名称标签
        value: 待清洗
      - action: 硬断言
        selector: 线索详情页-任务tab-最底下的任务状态
        value: 已完成
      - action: 硬断言
        selector: 线索详情页- 任务tab-最底下的任务预约时间标签
        value: 预约时间
      - action: 硬断言
        selector: 线索详情页- 任务tab-最底下的任务完成时间标签
        value: 完成时间
#    steps:
#      - use_module: login_steps
#      - use_module: create_store_unvisited_leads_steps
#      - action: 刷新
#        selector: ''
#
#      - action: 点击
#        selector: 底部菜单-客户
#
#      - action: 点击
#        selector: 客户列表-搜索
#
#      - action: 输入
#        selector: 客户列表查询条件输入框
#        value: ${phone_num}
#      - action: 点击
#        selector: 客户列表_执行搜索
#
#      - action: 点击
#        selector: 客户列表查询结果第一条填跟进按钮
#
#      - action: 点击
#        selector: 客户详情页面点击填跟进
#
#
#        selector: 线索详情页面-任务tab-第一个任务点击填跟进
#
#      - action: 验证
#        selector: 任务填跟进页面标题
#        value: 待回电任务
#      - action: 点击
#        selector: 购车周期-存疑
#
#      - action: 点击
#        selector: 任务填跟进页-跟进结果-战败
#
#      - action: 点击
#        selector: 任务填跟进页-跟进结果-战败-一级战败因素选项
#
#      - action: 点击
#        selector: 任务填跟进页-跟进结果-战败-二级战败因素输入框
#
#      - action: 点击
#        selector: 任务填跟进页-跟进结果-战败-二级战败因素弹框-第一个选项
#
#      - action: 点击
#        selector: 任务填跟进页-二级战败因素弹框-确认按钮
#
#      - action: 点击
#        selector: 任务填跟进页-提交跟进记录按钮
#
#      - action: 验证
#        selector: 线索详情页面标题-线索状态2
#        value: 战败
#      - action: 刷新
#        selector: ''
#
#      - action: 验证
#        selector: 底部菜单-工作
#        value: 工作