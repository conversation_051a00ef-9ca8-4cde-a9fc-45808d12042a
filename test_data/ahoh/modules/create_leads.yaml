create_store_unvisited_leads_steps: # APP中管家创建未到店的线索
  - action: 点击
    selector: 底部菜单-工作
    value: ''
  - action: 点击
    selector: 创建用户
    value: ''
  - action: 输入
    selector: 客户姓名
    value: 客户名称
  - action: faker
    data_type: mobile
    variable_name: phone_num
  - action: 输入
    selector: 手机号
    value: ${phone_num}
  - action: 点击
    selector: 是否已到店
  - action: 点击
    selector: 新增用户按钮

create_store_testDriveTask_lead_steps: #创建到店线索和试驾任务
  - action: 点击
    selector: 底部菜单-工作
    value: ''
  - action: 点击
    selector: 创建用户
    value: ''
  - action: 输入
    selector: 客户姓名
    value: 客户名称
  - action: faker
    data_type: mobile
    variable_name: phone_num
  - action: 输入
    selector: 手机号
    value: ${phone_num}
  - action: 点击
    selector: 到店类型-到门店
    value: ''
  - action: 点击
    selector: 到店诉求-试乘试驾
    value: ''
  - action: 点击
    selector: 试驾车系选择框
    value: ''
  - action: 验证
    selector: 选择试驾半屏页title
    value: 选择试驾
  - action: 点击
    selector: 选择试驾半屏页-第一个试驾车
    value: ''
  - action: 点击
    selector: 选择试驾半屏页-确定按钮
    value: ''
  - action: 点击
    selector: 新增用户按钮

