test_data:
  test_quick_quote:
    description: "30分钟报价"
    steps:
      - action: goto
        value: "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100"
      - action: click
        selector: 30分钟报价
      - action: assert_text
        selector: 极速报价
        value: 极速报价
      - action: assert_text
        selector: simple_cont_text_div
        value: 专业选车 全城低价
      - action: click
        selector: 意向车系
      - action: click
        selector: 选中车系
      - action: store_text
        selector: 选中车系
        variable_name: 选中车系
      - action: assert_text
        selector: 确认选择
        value: 确认选择(1)
      - action: click
        selector: 确认选择
      - action: assert_text
        selector: 车系名称
        value: ${选中车系}

  test_insured:
    description: "买贵必赔"
    steps:
      - action: goto
        value: "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100"
      - action: click
        selector: 买贵必赔
      - action: assert_url
        value: https://energyspace.m.autohome.com.cn/ahoh-marketing/insured?pvareaid=6865276&cityId=110100
      - action: click
        selector: 买贵必赔立即领取
      - action: assert_text
        selector: 暖心礼标题
        value: 领价值1280元暖心礼
      - action: click
        selector: 暖心礼关闭
      - action: click
        selector: 权益详细规则
      - action: assert_url
        value: https://mf.autohome.com.cn/v3/4806

#  test_charging_stations:
#    description: "送充电桩"
#    steps:
#      - action: goto
#        value: "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100"
#      - action: click
#        selector: 送充电桩
#      - action: assert_url
#        value: https://energyspace.m.autohome.com.cn/ahoh-marketing/insured?pvareaid=6865272&cityId=110100&pageType=chargingPile
#      - action: click
#        selector: 充电桩立即领取
#      - action: assert_text
#        selector: 领新春礼包标题
#        value: 领新春礼包
#      - action: click
#        selector: 领新春礼包关闭
#      - action: click
#        selector: 活动规则
#      - action: assert_url
#        value: https://mf.autohome.com.cn/v3/8167

  test_test_drive_owner:
    description: "试驾车主"
    steps:
      - action: goto
        value: "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100"
      - action: click
        selector: 试驾车主
      - action: assert_url
        value: https://mf.autohome.com.cn/v3/8589

  test_old_cars:
    description: "旧车估值"
    steps:
      - action: goto
        value: "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100"
      - action: click
        selector: 旧车估值
      - action: assert_text
        selector: 极速报价
        value: 高价回收旧车
      - action: click
        selector: 我的车型
      - action: store_text
        selector: 车系
        variable_name: 车系
      - action: click
        selector: 车系
        variable_name: 车系
      - action: store_text
        selector: 车型
        variable_name: 车型
      - action: click
        selector: 车型
      - action: assert_text
        selector: 回显车系车型
        value: ${车系}${车型}

  test_store_information:
    description: "门店信息"
    steps:
      - action: goto
        value: "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100"
      - action: click
        selector: 空间站名称
      - action: click
        selector: 取消
      - action: assert_text
        selector: 空间站名称
        value: 汽车之家空间站·北京站
      - action: click
        selector: 空间站地址
      - action: click
        selector: 取消
      - action: assert_text
        selector: 空间站地址
        value: 北辰亚运村汽车交易市场内综合交易大厅
      - action: click
        selector: 营业执照

  test_gold_sales:
    description: "金牌买手"
    steps:
      - action: goto
        value: "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100"
      - action: assert_text
        selector: 金牌销售
        value: 金牌销售
      - action: assert_text
        selector: 买手文案
        value: 全城询底价 专业帮买车
      - action: assert_text
        selector: 电话
        value: 电话
      - action: assert_text
        selector: 报价
        value: 真实在线报价
      - action: expect_popup
        selector: 金牌销售
        variable_name: pop_index
      - action: assert_url
        value: https://work.weixin.qq.com/ca/cawcde008b52661817?customer_channel=6866165--110100
      - action: close_window
      - action: assert_url
        value: https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100
      - action: wait
      - action: expect_popup
        selector: 报价
        variable_name: pop_index
      - action: assert_url
        value: https://work.weixin.qq.com/ca/cawcde0ceb320e0488?customer_channel=6865378--110100
      - action: close_window
      - action: assert_url
        value: https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100

  test_brand_screening:
    description: "品牌筛选"
    steps:
      - action: goto
        value: "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100"
      - action: assert_text
        selector: 买车意向
        value: 您的买车意向是？
      - action: click
        selector: 品牌
      - action: click
        selector: 字母导航
      - action: store_text
        selector: 字母导航
        variable_name: 字母导航
      - action: store_text
        selector: 置顶字母导航
        variable_name: 置顶字母导航
      - action: assert_text
        selector: 置顶字母导航
        value: ${字母导航}
      - action: click
        selector: 哈弗
      - action: click
        selector: 极狐
      - action: click
        selector: 零跑
      - action: assert_text
        selector: 确认按钮
        value: 确认选择 (3)
      - action: click
        selector: 清除
      - action: assert_text
        selector: 确认按钮
        value: 确认选择
      - action: click
        selector: 哈弗
      - action: click
        selector: 极狐
      - action: click
        selector: 零跑
      - action: click
        selector: 确认按钮
      - action: assert_text
        selector: 已选品牌数量
        value: 3个品牌

  test_car_card:
    description: "车卡"
    steps:
      - action: goto
        value: "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100"
      - action: store_text
        selector: 车卡优惠金额
        variable_name: 车卡优惠金额
      - action: click
        selector: 获取底价
      - action: store_text
        selector: 留资弹窗优惠金额
        variable_name: 留资弹窗优惠金额
      - action: assert_text
        selector: 留资弹窗优惠金额
        value: ${车卡优惠金额}
      - action: click
        selector: 关闭
      - action: expect_popup
        selector: 一对一报价
        variable_name: pop_index
      - action: assert_url
        value: https://work.weixin.qq.com/ca/cawcde96c9ccc668a9?customer_channel=6866154-5769-110100
      - action: close_window
      - action: assert_url
        value: https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100

  test_car_details:
    description: "车系详情"
    steps:
      - action: goto
        value: "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100"
      - action: store_text
        selector: 车卡车系名称
        variable_name: 车卡车系名称
      - action: click
        selector: 车卡车系名称
      - action: store_text
        selector: 详情车系名称
        variable_name: 详情车系名称
      - action: assert_text
        selector: 详情车系名称
        value: ${车卡车系名称}
      - action: assert_text
        selector: 门店信息
        value: 门店信息
      - action: assert_text
        selector: 购车计算
        value: 购车计算
      - action: assert_text
        selector: 以旧换新
        value: 以旧换新
      - action: assert_text
        selector: 试驾服务
        value: 试驾服务

  test_car_details_info:
    description: "门店信息"
    steps:
      - action: goto
        value: "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4?cityId=110100"
      - action: click
        selector: 车卡车系名称
      - action: click
        selector: 门店信息
      - action: assert_text
        selector: detail_one_store
        value: 导航到店
      - action: assert_text
        selector: detail_call
        value: 电话咨询
      - action: assert_text
        selector: detail_question
        value: 在线咨询




































