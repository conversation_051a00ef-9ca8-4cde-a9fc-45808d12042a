elements:
  30分钟报价: div.header-nav-item
  极速报价: "h2.fill-info-popup-header-title" # 极速报价标题
  simple_cont_text_div: "div.simple-cont-text" # 简易内容文本
  意向车系: //input[@id='van-field-2-input'] # 意向车系选择框容器
  选中车系: "div.info-name" # 车型名称 AION S MAX
  确认选择: //div[contains(text(),'确认选择(1)')]
  车系名称: .select-series--value # 弹窗中第一个系列名称

  买贵必赔: //div[@class='header']//div[2]//div[1] # 点击买贵必赔获取保障按钮
  买贵必赔立即领取: "button:has-text('立即领取')" # 点击立即领取按钮
  暖心礼标题: "h2.fill-info-popup-header-title" # 领价值1280元暖心礼标题
  暖心礼关闭: ".fill-info-popup-close" # 点击关闭弹窗按钮
  权益详细规则: "text=权益详细规则" # 点击权益详细规则文本

#  送充电桩: text=送充电桩 立即领取 # 点击买贵必赔获取保障按钮
#  充电桩立即领取: "button:has-text('立即领取')" # 点击立即领取按钮
#  领新春礼包标题: "h2.fill-info-popup-header-title" # 领新春礼包标题
#  领新春礼包关闭: ".fill-info-popup-close" # 点击关闭弹窗按钮
#  活动规则: "text=活动规则" # 点击活动规则文本

  试驾车主: //div[@class='header']//div[3]//div[1]

  旧车估值: //div[@class='header']//div[4]//div[1]
  高价回收旧车: "h2.fill-info-popup-header-title" # 高价回收旧车标题
  我的车型: .van-cell.van-field.choose-used-model-field # 选择已用车型
  车系: //div[contains(text(),'一汽奥迪A5L')] # 系列信息名称
  车型: //div[@class='select-series-box__list-spec']//div[4]//div[1]
  回显车系车型: //span[@class='select-series--value'] # 车型系列选择的值，显示已选车型

  空间站名称: //div[@class='name'] # 点击“汽车之家空间站·北京站”
  取消: button[class='van-action-sheet__cancel'] # 点击“取消”按钮
  空间站地址: //div[@class='store-info-cont-address'] # 点击“北辰亚运村汽车交易市场内综合交易大厅”
  营业执照: img[alt='证'] # 点击图片“证”
  空白区域: //body

  金牌销售: //div[@class='store-sales-cont-name'] # 点击“金牌销售”
  买手文案: //div[@class='store-sales-cont-tip'] # 点击“全城询底价 专业帮买车”
  电话: div[class='store-sales-contact-phone'] span
  报价: //div[@class='store-sales-contact-online']/span

  买车意向: .filter-title
  品牌: //span[contains(text(),'品牌')]
  字母导航: span[data-index='H']
  置顶字母导航: .van-index-anchor.van-index-anchor--sticky.van-hairline--bottom
  哈弗: //li[@title='哈弗']//div[@class='brand-list__part--check']
  极狐: //li[@title='ARCFOX极狐']//div[@class='brand-list__part--check']
  零跑: //li[@title='零跑汽车']//div[@class='brand-list__part--check']
  确认按钮: //div[@class='brand-list__btn']//div//div[1]
  清除: .checked-brand__clear
  已选品牌数量: //span[@class='checked-brand-num']

  车卡优惠金额: (//div[@class='max-discount-tag tag'])[1]/span
  获取底价: (//div[contains(text(),'获取底价')])[1]
  留资弹窗优惠金额: h3[class='car-card-deals-title'] span[class='color']
  关闭: //div[@class='fill-info-popup-close']
  一对一报价: //div[@class='highlight-price-contact']

  车卡车系名称: (//div[@class='series-card-name'])[1]
  车卡: (//div[@we-com-info='[object Object]'])[3]
  详情车系名称: //div[@class='spec-select__name']
  门店信息: //span[contains(text(),"门店信息")]
  购车计算: //span[contains(text(),'购车计算')]
  以旧换新: //span[contains(text(),'以旧换新')]
  试驾服务: //span[contains(text(),'试驾服务')]

  choice_text: //div[@aria-selected="true"]/span
  choice_car_clc: //span[contains(text(),"购车计算")]
  choice_trade_in: //span[contains(text(),"以旧换新")]
  choice_test_drive: //span[contains(text(),"试驾服务")]
  detail_one_store: //div[@class="oneStore"]
  detail_call: //div[@class="call"]
  detail_question: //div[@class="question"]

































