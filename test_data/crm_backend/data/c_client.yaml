test_data:
  test_car_series_search_band:
    - description: "搜索大众"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系排序配置
        - action: click
          selector: order_品牌
        - action: click
          selector: order_品牌_大众
        - action: assert_text
          selector: order_品牌
          value: 大众
        - action: monitor_request
          url_pattern: "api/sysSeriesSort/getSeriesSortListPage"
          selector: 查询
          assert_params:
            $.brandId: 1
        - action: assert_text
          selector: order_tb_品牌
          value: 大众
        - action: click
          selector: order_车系
  #        - action: store_text
  #          value: 车系
  #          selector:
  test_car_series_search_series:
    - description: "搜索大众车系"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系排序配置
        - action: click
          selector: order_品牌
        - action: click
          selector: order_品牌_大众
        #        - action: monitor_response
        #          selector: order_品牌_大众
        #          url_pattern: "/api/car/getSeriesAll"
        #          save_params:
        #            $..seriesName: series_name
        #            $..seriesId: series_id
        - action: click
          selector: order_车系
        - action: click
          selector: order_车系_save
        - action: assert_text
          selector: order_车系
          value: ${series_name}
        - action: monitor_request
          url_pattern: "api/sysSeriesSort/getSeriesSortListPage"
          selector: 查询
          assert_params:
            $.brandId: 1
        - action: assert_text
          selector: order_tb_车系
          value: ${series_name}
  test_car_series_reset:
    - description: "重置搜索"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系排序配置
        - action: click
          selector: order_品牌
        - action: click
          selector: order_品牌_大众
        - action: assert_text
          selector: order_品牌
          value: 大众
        - action: click
          selector: 重置
        - action: assert_text
          selector: order_品牌
          value: 请选择品牌
        - action: assert_text
          selector: order_车系
          value: 请选择车系
  # 推荐试驾
  test_recommend_search:
    - description: "搜索推荐试驾"
      steps:
        - use_module: click_c
        - action: click
          selector: 推荐试驾
        - action: fill
          selector: recommend_组合名称
          value: ${recommend_name}
        - action: click
          selector: recommend_城市
        - action: fill
          selector: recommend_城市_input
          value: ${recommend_city}
        - action: click
          selector: recommend_城市_value
        - action: click
          selector: recommend_状态
        - action: click
          selector: recommend_状态_启用
        - action: monitor_request
          selector: 查询
          url_pattern: /api/combinationConfig/getCombinationConfigListPage
          assert_params:
            $.combinationName: ${recommend_name}
            $.cityId: 310100
            $.status: 0
  test_recommend_name:
    - description: "组合名称"
      steps:
        - use_module: click_c
        - action: click
          selector: 推荐试驾
        - action: fill
          selector: recommend_组合名称
          value: ${recommend_name}
        - action: click
          selector: 查询
        - action: assert_text_contains
          selector: recommend_tb_组合名称
          value: ${recommend_name}
  test_recommend_city:
    - description: "投放城市"
      steps:
        - use_module: click_c
        - action: click
          selector: 推荐试驾
        - action: click
          selector: recommend_城市
        - action: fill
          selector: recommend_城市_input
          value: ${recommend_city}
        - action: click
          selector: recommend_城市_value
        - action: click
          selector: 查询
        - action: assert_text
          selector: recommend_tb_城市
          value: ${recommend_city}
  test_recommend_status:
    - description: "启用状态"
      steps:
        - use_module: click_c
        - action: click
          selector: 推荐试驾
        - action: click
          selector: recommend_状态
        - action: click
          selector: recommend_状态_启用
        - action: click
          selector: 查询
        - action: ASSERT_ATTRIBUTE
          attribute: aria-checked
          selector: recommend_tb_状态
          value: "true"
    - description: "禁用状态"
      steps:
        - use_module: click_c
        - action: click
          selector: 推荐试驾
        - action: click
          selector: recommend_状态
        - action: click
          selector: recommend_状态_关闭
        - action: click
          selector: 查询
  #        - action: ASSERT_ATTRIBUTE
  #          attribute: aria-checked
  #          selector: recommend_tb_状态
  #          value: "false"
  test_recommend_reset:
    - description: "重置"
      steps:
        - use_module: click_c
        - action: click
          selector: 推荐试驾
        - action: fill
          selector: recommend_组合名称
          value: ${recommend_name}
        - action: click
          selector: recommend_城市
        - action: fill
          selector: recommend_城市_input
          value: ${recommend_city}
        - action: click
          selector: recommend_城市_value
        - action: click
          selector: recommend_状态
        - action: click
          selector: recommend_状态_启用
        - action: click
          selector: 重置
        - action: assert_value
          selector: recommend_组合名称
          value: ""
        - action: assert_text
          selector: recommend_城市
          value: "请选择投放城市"
        - action: assert_text
          selector: recommend_状态
          value: "请选择"
  test_recommend_new:
    - description: "推荐试驾新建"
      steps:
        - use_module: click_c
        - action: click
          selector: 推荐试驾
        - action: click
          selector: 新建
        - action: assert_text
          selector: title
          value: "试驾组合配置"
  test_recommend_edit:
    - description: "推荐试驾编辑"
      steps:
        - use_module: click_c
        - action: click
          selector: 推荐试驾
        - action: fill
          selector: recommend_组合名称
          value: ${recommend_name}
        - action: click
          selector: recommend_城市
        - action: fill
          selector: recommend_城市_input
          value: ${recommend_city}
        - action: click
          selector: recommend_城市_value
        - action: click
          selector: recommend_状态
        - action: click
          selector: recommend_状态_启用
        - action: click
          selector: 查询
        - action: click
          selector: recommend_tb_编辑
        - action: assert_text
          selector: title
          value: "试驾组合配置"
        - action: assert_value
          selector: recommend_编辑_名称
          value: ${recommend_name}
        - action: assert_text_contains
          selector: recommend_编辑_城市
          value: ${recommend_city}
        - action: assert_text
          selector: recommend_编辑_状态
          value: "启用"

  test_resource_search_title:
    - description: "搜索资源名称"
      steps:
        - use_module: click_c
        - action: click
          selector: 资源位配置
        - action: fill
          selector: resource_标题
          value: 测试
        - action: click
          selector: 查询
        - action: assert_text_contains
          selector: resource_tb_title
          value: 测试
    - description: "搜索pvareaid"
      steps:
        - use_module: click_c
        - action: click
          selector: 资源位配置
        - action: fill
          selector: resource_标题
          value: "6860645"
        - action: click
          selector: 查询
        - action: assert_text
          selector: resource_tb_pvareaid
          value: "6860645"
  test_resource_search_type:
    - description: "搜索资源类型"
      steps:
        - use_module: click_c
        - action: click
          selector: 资源位配置
        - action: click
          selector: resource_类型
        - action: click
          selector: resource_类型_1
        - action: monitor_request
          selector: 查询
          url_pattern: /api/admin/cPosition/config/list
          assert_params:
            $.positionType: 1
        - action: click
          selector: resource_类型
        - action: click
          selector: resource_类型_2
        - action: monitor_request
          selector: 查询
          url_pattern: /api/admin/cPosition/config/list
          assert_params:
            $.positionType: 2

  test_resource_search_status:
    - description: "资源位配置启用状态"
      steps:
        - use_module: click_c
        - action: click
          selector: 资源位配置
        - action: click
          selector: resource_状态
        - action: click
          selector: resource_状态_启用
        - action: monitor_request
          selector: 查询
          url_pattern: /api/admin/cPosition/config/list
          assert_params:
            $.configState: 1
        - action: ASSERT_ATTRIBUTE
          attribute: aria-checked
          selector: city_rights_tb_状态
          value: "1"
    - description: "资源位配置禁用状态"
      steps:
        - use_module: click_c
        - action: click
          selector: 资源位配置
        - action: click
          selector: resource_状态
        - action: click
          selector: resource_状态_禁用
        - action: monitor_request
          selector: 查询
          url_pattern: /api/admin/cPosition/config/list
          assert_params:
            $.configState: 0
        - action: ASSERT_ATTRIBUTE
          attribute: aria-checked
          selector: city_rights_tb_状态
          value: "0"
  test_resource_reset:
    - description: "资源位配置重置"
      steps:
        - use_module: click_c
        - action: click
          selector: 资源位配置
        - action: fill
          selector: resource_标题
          value: 测试
        - action: click
          selector: resource_类型
        - action: store_text
          selector: resource_类型_1
          variable_name: resource_type
        - action: click
          selector: resource_类型_1
        - action: click
          selector: resource_状态
        - action: click
          selector: resource_状态_启用
        - action: click
          selector: 重置
        - action: assert_value
          selector: resource_标题
          value: ""
        - action: assert_text
          selector: resource_类型
          value: "请选择"
        - action: assert_text
          selector: resource_状态
          value: "请选择"
  test_resource_new:
    - description: "资源位配置新建"
      steps:
        - use_module: click_c
        - action: click
          selector: 资源位配置
        - action: wait
        - action: click
          selector: 新建
        - action: assert_text
          selector: 弹框标题
          value: "新增"
        - action: assert_value
          selector: resource_新建_名称
          value: ""
        - action: assert_value
          selector: resource_新建_pvareaid
          value: ""
        - action: assert_text
          selector: resource_新建_状态
          value: "启用"
        - action: fill
          selector: resource_新建_名称
          value: 测试
        - action: fill
          selector: resource_新建_pvareaid
          value: "123456"
        - action: assert_value
          selector: resource_新建_pvareaid
          value: "123456"
        - action: assert_value
          selector: resource_新建_名称
          value: 测试
        - action: click
          selector: 弹框关闭
        - action: assert_be_hidden
          selector: 弹框

  test_resource_edit:
    - description: "资源位配置编辑"
      steps:
        - use_module: click_c
        - action: click
          selector: 资源位配置
        - action: fill
          selector: resource_标题
          value: 测试
        - action: click
          selector: resource_类型
        - action: store_text
          selector: resource_类型_1
          variable_name: resource_type
        - action: click
          selector: resource_类型_1
        - action: click
          selector: resource_状态
        - action: click
          selector: resource_状态_禁用
        - action: click
          selector: 查询
        - action: store_text
          selector: resource_tb_pvareaid
          variable_name: resource_pvareaid
        - action: click
          selector: resource_tb_编辑
        - action: assert_text
          selector: 弹框标题
          value: "编辑"
#        - action: assert_text_contains # 没有找到相关定位方式
#          selector: resource_新建_名称
#          value: 测试
        - action: assert_value
          selector: resource_新建_pvareaid
          value: ${resource_pvareaid}
        - action: assert_text
          selector: resource_编辑_类型
          value: ${resource_type}
        - action: assert_text
          selector: resource_编辑_状态
          value: "禁用"
        - action: click
          selector: 弹框关闭
        - action: assert_be_hidden
          selector: 弹框
  #优惠权益
  test_benefits_search:
    - description: "搜索权益配置"
      steps:
        - use_module: click_c
        - action: click
          selector: 优惠权益配置
        - action: assert_text
          selector: title
          value: 优惠权益配置
        - action: click
          selector: benefits_位置
        - action: click
          selector: benefits_位置_频道页
        - action: click
          selector: benefits_权益
        - action: click
          selector: benefits_权益_购车礼金
        - action: click
          selector: benefits_品牌
        - action: click
          selector: benefits_品牌_Abarth
        - action: click
          selector: benefits_车系
        - action: click
          selector: benefits_车系_Abarth_500新能源
        - action: click
          selector: benefits_城市
        - action: click
          selector: benefits_城市_展开
        - action: click
          selector: benefits_城市_北京
        - action: monitor_request
          selector: 查询
          url_pattern: api/admin/seriesCityRight/pageList
          assert_params:
            $.positionId: 1
            $.rightType: 1
            $.brandId: 524
            $.seriesId: 7001
            $.cityId: 110100
  test_benefits_reset:
    - description: "重置权益配置"
      steps:
        - use_module: click_c
        - action: click
          selector: 优惠权益配置
        - action: click
          selector: benefits_位置
        - action: click
          selector: benefits_位置_频道页
        - action: click
          selector: benefits_权益
        - action: click
          selector: benefits_权益_购车礼金
        - action: click
          selector: benefits_品牌
        - action: click
          selector: benefits_品牌_Abarth
        - action: click
          selector: benefits_车系
        - action: click
          selector: benefits_车系_Abarth_500新能源
        - action: click
          selector: benefits_城市
        - action: click
          selector: benefits_城市_展开
        - action: click
          selector: benefits_城市_北京
        - action: click
          selector: 重置
        - action: assert_text
          selector: benefits_位置
          value: "请选择位置"
        - action: assert_text
          selector: benefits_权益
          value: "请选择权益"
        - action: assert_text
          selector: benefits_品牌
          value: "请选择品牌"
        - action: assert_text
          selector: benefits_车系
          value: "请选择车系"
        - action: assert_text
          selector: benefits_城市
          value: "请选择"
  test_benefits_location:
    - description: "搜索位置"
      steps:
        - use_module: click_c
        - action: click
          selector: 优惠权益配置
        - action: click
          selector: benefits_位置
        - action: click
          selector: benefits_位置_频道页
        - action: monitor_request
          selector: 查询
          url_pattern: api/admin/seriesCityRight/pageList
          assert_params:
            $.positionId: 1
        - action: assert_text
          selector: benefits_tb_位置
          value: 频道页
  test_benefits_right:
    - description: "搜索权益"
      steps:
        - use_module: click_c
        - action: click
          selector: 优惠权益配置
        - action: click
          selector: benefits_权益
        - action: click
          selector: benefits_权益_购车礼金
        - action: monitor_request
          selector: 查询
          url_pattern: api/admin/seriesCityRight/pageList
          assert_params:
            $.rightType: 1
        - action: assert_text
          selector: benefits_tb_权益
          value: 购车礼金
  test_benefits_city:
    - description: "搜索城市"
      steps:
        - use_module: click_c
        - action: click
          selector: 优惠权益配置
        - action: click
          selector: benefits_城市
        - action: click
          selector: benefits_城市_展开
        - action: click
          selector: benefits_城市_北京
        - action: monitor_request
          selector: 查询
          url_pattern: api/admin/seriesCityRight/pageList
          assert_params:
            $.cityId: 110100
        - action: assert_text_contains
          selector: benefits_tb_城市
          value: 北京

  test_benefits_new:
    - description: "优惠权益配置新建"
      steps:
        - use_module: click_c
        - action: click
          selector: 优惠权益配置
        - action: click
          selector: benefits_新建活动
        - action: assert_text
          selector: 弹框标题
          value: "权益配置"
        - action: click
          selector: 弹框关闭
        - action: assert_be_hidden
          selector: 弹框

  #城市权益配置
  test_city_rights_search:
    - description: "搜索城市权益配置"
      steps:
        - use_module: click_c
        - action: click
          selector: 城市权益配置
        - action: fill
          selector: city_rights_标题_input
          value: 测试
        - action: click
          selector: city_rights_类型
        - action: click
          selector: city_rights_试驾礼
        - action: click
          selector: city_rights_城市
        - action: click
          selector: city_rights_城市_展开
        - action: click
          selector: city_rights_城市_北京
        - action: click
          selector: city_rights_状态
        - action: click
          selector: city_rights_状态_启用
        - action: monitor_request
          selector: 查询
          url_pattern: /api/cityEquity/pageList
          assert_params:
            $.equityName: 测试
            $.equityType: 1
            $.status: 0
            $.cityId: 110100

  test_city_rights_reset:
    - description: "重置城市权益配置"
      steps:
        - use_module: click_c
        - action: click
          selector: 城市权益配置
        - action: fill
          selector: city_rights_标题_input
          value: 测试
        - action: click
          selector: city_rights_类型
        - action: click
          selector: city_rights_试驾礼
        - action: click
          selector: city_rights_城市
        - action: click
          selector: city_rights_城市_展开
        - action: click
          selector: city_rights_城市_北京
        - action: click
          selector: 重置
        - action: assert_text
          selector: city_rights_标题
          value: ""
        - action: assert_text
          selector: city_rights_类型
          value: "请选择"
        - action: assert_text
          selector: city_rights_城市
          value: "请选择"
        - action: assert_text
          selector: city_rights_状态
          value: "请选择"

  test_city_rights_type:
    - description: "搜索城市权益配置类型"
      steps:
        - use_module: click_c
        - action: click
          selector: 城市权益配置
        - action: click
          selector: city_rights_类型
        - action: click
          selector: city_rights_试驾礼
        - action: click
          selector: 查询
        - action: assert_text
          selector: city_rights_tb_类型
          value: 试驾礼
  test_city_rights_city:
    - description: "搜索城市权益配置城市"
      steps:
        - use_module: click_c
        - action: click
          selector: 城市权益配置
        - action: click
          selector: city_rights_城市
        - action: click
          selector: city_rights_城市_展开
        - action: click
          selector: city_rights_城市_北京
        - action: click
          selector: 查询
        - action: assert_text_contains
          selector: city_rights_tb_城市
          value: 北京
  test_city_rights_status:
    - description: "搜索城市权益配置启用状态"
      steps:
        - use_module: click_c
        - action: click
          selector: 城市权益配置
        - action: click
          selector: city_rights_状态
        - action: click
          selector: city_rights_状态_启用
        - action: click
          selector: 查询
        - action: ASSERT_ATTRIBUTE
          attribute: aria-checked
          selector: city_rights_tb_状态
          value: "true"
    -  description: "搜索城市权益配置关闭状态"
       steps:
        - use_module: click_c
        - action: click
          selector: 城市权益配置
        - action: click
          selector: city_rights_状态
        - action: click
          selector: city_rights_状态_关闭
        - action: click
          selector: 查询
        - action: ASSERT_ATTRIBUTE
          attribute: aria-checked
          selector: city_rights_tb_状态
          value: "false"
  test_city_rights_new:
    - description: "城市权益配置新建"
      steps:
        - use_module: click_c
        - action: click
          selector: 城市权益配置
        - action: click
          selector: 新建
        - action: assert_text
          selector: 弹框标题
          value: "权益配置"
        - action: click
          selector: 弹框关闭
        - action: assert_be_hidden
          selector: 弹框
  test_city_rights_edit:
    - description: "城市权益配置编辑"
      steps:
        - use_module: click_c
        - action: click
          selector: 城市权益配置
        - action: click
          selector: city_rights_类型
        - action: click
          selector: city_rights_试驾礼
        - action: click
          selector: city_rights_城市
        - action: click
          selector: city_rights_城市_展开
        - action: click
          selector: city_rights_城市_北京
        - action: click
          selector: city_rights_状态
        - action: click
          selector: city_rights_状态_启用
        - action: click
          selector: 查询
        - action: click
          selector: city_rights_tb_编辑
        - action: assert_text
          selector: 弹框标题
          value: "权益配置"
        - action: assert_text
          selector: city_rights_编辑_类型
          value: 试驾礼
        - action: assert_text
          selector: city_rights_编辑_状态
          value: 启用
        - action: click
          selector: 弹框关闭
        - action: assert_be_hidden
          selector: 弹框

  #精彩现场
  test_live_search:
    - description: "搜索精彩现场"
      steps:
        - use_module: click_c
        - action: click
          selector: 精彩现场
        - action: fill
          selector: live_标题
          value: 测试
        - action: fill
          selector: live_城市
          value: 北京
        - action: click
          selector: live_来源
        - action: click
          selector: live_来源_UGC
        - action: click
          selector: live_启用状态
        - action: click
          selector: live_启用状态_启用
        - action: monitor_request
          selector: 查询
          url_pattern: /api/topicPostInfo/getTopicPostInfoListPage
          assert_params:
            $.topicTitle: 测试
            $.cityName: 北京
            $.topicSource: 0
            $.status: 0
  test_live_source:
    - description: "搜索精彩现场来源"
      steps:
        - use_module: click_c
        - action: click
          selector: 精彩现场
        - action: click
          selector: live_来源
        - action: click
          selector: live_来源_UGC
        - action: click
          selector: 查询
        - action: assert_text
          selector: live_tb_来源
          value: UGC
  test_live_status:
    - description: "搜索精彩现场启用状态"
      steps:
        - use_module: click_c
        - action: click
          selector: 精彩现场
        - action: click
          selector: live_启用状态
        - action: click
          selector: live_启用状态_启用
        - action: click
          selector: 查询
        - action: ASSERT_ATTRIBUTE
          attribute: aria-checked
          selector: live_tb_启用状态
          value: "true"
    - description: "搜索精彩现场关闭状态"
      steps:
        - use_module: click_c
        - action: click
          selector: 精彩现场
        - action: click
          selector: live_启用状态
        - action: click
          selector: live_启用状态_禁用
        - action: click
          selector: 查询
        - action: ASSERT_ATTRIBUTE
          attribute: aria-checked
          selector: live_tb_启用状态
          value: "false"
  test_live_rest:
    - description: "重置精彩现场"
      steps:
        - use_module: click_c
        - action: click
          selector: 精彩现场
        - action: fill
          selector: live_标题
          value: 测试
        - action: fill
          selector: live_城市
          value: 北京
        - action: click
          selector: live_来源
        - action: click
          selector: live_来源_UGC
        - action: click
          selector: live_启用状态
        - action: click
          selector: live_启用状态_启用
        - action: click
          selector: 重置
        - action: assert_value
          selector: live_标题
          value: ""
        - action: assert_value
          selector: live_城市
          value: ""
        - action: assert_text
          selector: live_来源
          value: "请选择"
        - action: assert_text
          selector: live_启用状态
          value: "请选择"
  test_live_new:
    - description: "精彩现场新建"
      steps:
        - use_module: click_c
        - action: click
          selector: 精彩现场
        - action: click
          selector: 新建
        - action: assert_text
          selector: title
          value: "精彩现场活动配置"

  test_car_config_search:
    - description: "搜索车系配置"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系配置
        - action: click
          selector: car_config_位置
        - action: click
          selector: car_config_位置_1
        - action: store_text
          selector: car_config_位置_1
          variable_name: config_location
        - action: click
          selector: car_config_城市
        - action: click
          selector: car_config_城市_北京
        - action: click
          selector: car_config_品牌
        - action: click
          selector: car_config_品牌_选择
        - action: click
          selector: car_config_主推
        - action: click
          selector: car_config_主推_是
        - action: click
          selector: car_config_类别
        - action: click
          selector: car_config_类别_A
        - action: click
          selector: car_config_启用状态
        - action: click
          selector: car_config_启用状态_开启
        - action: monitor_request
          selector: 查询
          url_pattern: api/operationSeriesConfig/getOperationSeriesConfigPageList
          assert_params:
            $.current: 1
            $.pageSize: 10
            $.cityId: 110100
            $.isMainRecommend: 1
            $.brandId: 15
            $.position: 1
            $.status: 1
            $.brandType: A
  test_car_config_reset:
    - description: "重置车系配置"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系配置
        - action: click
          selector: car_config_位置
        - action: click
          selector: car_config_位置_1
        - action: click
          selector: car_config_城市
        - action: click
          selector: car_config_城市_北京
        - action: click
          selector: car_config_品牌
        - action: click
          selector: car_config_品牌_选择
        - action: click
          selector: car_config_主推
        - action: click
          selector: car_config_主推_是
        - action: click
          selector: car_config_类别
        - action: click
          selector: car_config_类别_A
        - action: click
          selector: car_config_启用状态
        - action: click
          selector: car_config_启用状态_开启
        - action: click
          selector: 重置
        - action: assert_text
          selector: car_config_位置
          value: "请选择位置"
        - action: assert_text
          selector: car_config_城市
          value: "请选择投放城市"
        - action: assert_text
          selector: car_config_品牌
          value: "请选择品牌"
        - action: assert_text
          selector: car_config_主推
          value: "请选择"
        - action: assert_text
          selector: car_config_类别
          value: "请选择"
        - action: assert_text
          selector: car_config_启用状态
          value: "请选择"
  test_car_config_location:
    - description: "搜索车系配置位置"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系配置
        - action: click
          selector: car_config_位置
        - action: store_text
          selector: car_config_位置_1
          variable_name: config_location
        - action: click
          selector: car_config_位置_1
        - action: click
          selector: 查询
        - action: assert_text
          selector: car_config_tb_位置
          value: ${config_location}
  test_car_config_city:
    - description: "搜索车系配置城市"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系配置
        - action: click
          selector: car_config_城市
        - action: click
          selector: car_config_城市_北京
        - action: click
          selector: 查询
        - action: assert_text_contains
          selector: car_config_tb_城市
          value: 北京
  test_car_config_brand:
    - description: "搜索车系配置品牌"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系配置
        - action: click
          selector: car_config_品牌
        - action: click
          selector: car_config_品牌_选择
        - action: click
          selector: 查询
        - action: assert_text
          selector: car_config_tb_品牌
          value: ${brand_name}
  test_car_config_main:
    - description: "搜索车系配置主推"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系配置
        - action: click
          selector: car_config_主推
        - action: click
          selector: car_config_主推_是
        - action: click
          selector: 查询
        - action: assert_text
          selector: car_config_tb_主推
          value: 是
    - description: "搜索车系配置非主推"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系配置
        - action: click
          selector: car_config_主推
        - action: click
          selector: car_config_主推_否
        - action: click
          selector: 查询
        - action: assert_text
          selector: car_config_tb_主推
          value: 否
  test_car_config_type:
    - description: "搜索车系配置类别"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系配置
        - action: click
          selector: car_config_类别
        - action: click
          selector: car_config_类别_A
        - action: click
          selector: 查询
        - action: assert_text
          selector: car_config_tb_类别
          value: A
  test_car_config_status:
    - description: "搜索车系配置启用状态"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系配置
        - action: click
          selector: car_config_启用状态
        - action: click
          selector: car_config_启用状态_开启
        - action: click
          selector: 查询
        - action: ASSERT_ATTRIBUTE
          attribute: aria-checked
          selector: car_config_tb_启用状态
          value: "true"
    - description: "搜索车系配置关闭状态"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系配置
        - action: click
          selector: car_config_启用状态
        - action: click
          selector: car_config_启用状态_关闭
        - action: click
          selector: 查询
        - action: ASSERT_ATTRIBUTE
          attribute: aria-checked
          selector: car_config_tb_启用状态
          value: "false"

  test_car_config_new:
    - description: "车系配置新建"
      steps:
        - use_module: click_c
        - action: click
          selector: 车系配置
        - action: click
          selector: 新建
        - action: assert_text
          selector: title
          value: "车系配置"

  test_operation_search:
      - description: "搜索运营投放配置"
        steps:
          - use_module: click_c
          - action: monitor_response
            selector: 运营投放配置
            url_pattern: /api/dict/getDictListByCategoryCode?categoryCode=operationConfig_position&_appid=app_ahoh_admin
            save_params:
              $.result[0].dictVal: dictVal
              $.result[0].dictKey: dictKey
          - action: assert_text
            selector: title
            value: "运营投放配置"
          - action: click
            selector: operation_位置
          - action: click
            selector: operation_位置_选项
          - action: monitor_request
            selector: 查询
            url_pattern: /api/operation/getOperationList
            assert_params:
              $.position: ${dictVal}
          - action: assert_text
            selector: operation_tb_位置
            value: ${dictKey}
  test_operation_new:
    - description: "运营投放配置新建"
      steps:
        - use_module: click_c
        - action: click
          selector: 运营投放配置
        - action: click
          selector: operation_新增活动
        - action: assert_text
          selector: //div[@class="ant-modal-header"]
          value: "活动配置"
  test_operation_reset:
    - description: "重置运营投放配置"
      steps:
        - use_module: click_c
        - action: click
          selector: 运营投放配置
        - action: click
          selector: operation_位置
        - action: click
          selector: operation_位置_选项
        - action: click
          selector: 重置
        - action: assert_text
          selector: operation_位置
          value: "请选择位置"
  test_image_new:
    - description: "图片处理工具新建"
      steps:
        - use_module: click_c
        - action: click
          selector: 图片处理工具
        - action: assert_text
          selector: title
          value: "图片处理工具"
        - action: upload
          selector: image_storeImage
          value: files/images/logo.png
        - action: store_attribute
          attribute: value
          selector: image_inputText
          variable_name: image_url
        - action: assert_value
          selector: image_inputText
          value: ${image_url}
          
          