test_data:
  test_channel_page_activity_approval_inquire:
    - description: "频道页活动审批查询"
      steps:
        - use_module: click_approval_center
        - action: click
          selector: 频道页活动审批
        - action: wait
        - action: fill
          selector: 活动名称搜索
          value: "门店活动001"
        - action: fill
          selector: 提审人搜索
          value: "香港综合店1"
        - action: click
          selector: 提审门店搜索
        - action: click
          selector: 选中门店
        - action: click
          selector: 审批状态搜索
        - action: click
          selector: 驳回
        - action: click
          selector: 查询
        - action: wait
        - action: assert_text
          selector: 活动名称
          value: "门店活动001"
        - action: assert_text
          selector: 提审门店
          value: "加盟商香港综合测试门店1"
        - action: assert_text
          selector: 提审人
          value: "香港综合店1"
        - action: assert_text
          selector: 审批状态
          value: "驳回"

  test_channel_page_activity_approval_inquire_null:
    - description: "频道页活动审批查询为空"
      steps:
        - use_module: click_approval_center
        - action: click
          selector: 频道页活动审批
        - action: wait
        - action: fill
          selector: 活动名称搜索
          value: "不存在的审批"
        - action: click
          selector: 查询
        - action: wait
        - action: assert_text
          selector: 暂无数据
          value: "暂无数据"

  test_channel_page_activity_approval_inquire_reset:
    - description: "频道页活动审批查询重置"
      steps:
        - use_module: click_approval_center
        - action: click
          selector: 频道页活动审批
        - action: wait
        - action: fill
          selector: 活动名称搜索
          value: "门店活动001"
        - action: fill
          selector: 提审人搜索
          value: "香港综合店1"
        - action: click
          selector: 提审门店搜索
        - action: click
          selector: 选中门店
        - action: click
          selector: 审批状态搜索
        - action: click
          selector: 驳回
        - action: click
          selector: 重置
        - action: assert_value
          selector: 活动名称搜索
          value: ""
        - action: assert_value
          selector: 提审人搜索
          value: ""
        - action: assert_value
          selector: 提审门店搜索
          value: ""
        - action: assert_value
          selector: 审批状态搜索
          value: ""

  test_channel_page_activity_configuration_inquire:
    - description: "频道页活动配置表查询"
      steps:
        - use_module: click_approval_center
        - action: click
          selector: 频道页活动配置表
        - action: wait
        - action: fill
          selector: 配置活动名称搜索
          value: "UI脚本专用001"
        - action: fill
          selector: 配置提审人搜索
          value: "测试店长"
        - action: click
          selector: 配置提审门店搜索
        - action: click
          selector: 配置选中门店
        - action: click
          selector: 配置审批状态搜索
        - action: click
          selector: 配置驳回
        - action: click
          selector: 配置活动位置搜索
        - action: click
          selector: 频道页双列车卡
        - action: click
          selector: 配置查询
        - action: wait
        - action: assert_text
          selector: 配置活动位置
          value: "频道页双列车卡"
        - action: assert_text
          selector: 配置活动名称
          value: "UI脚本专用001"
        - action: assert_text
          selector: 配置提审门店
          value: "加盟商香港综合测试门店1"
        - action: assert_text
          selector: 配置提审人
          value: "测试店长"
        - action: assert_text
          selector: 配置审批状态
          value: "驳回"

  test_channel_page_activity_configuration_inquire_null:
    - description: "频道页活动配置表查询为空"
      steps:
        - use_module: click_approval_center
        - action: click
          selector: 频道页活动配置表
        - action: wait
        - action: fill
          selector: 配置活动名称搜索
          value: "不存在的内容"
        - action: click
          selector: 配置查询
        - action: assert_text
          selector: 暂无数据
          value: "暂无数据"

  test_channel_page_activity_configuration_inquire_reset:
    - description: "频道页活动配置表查询重置"
      steps:
        - use_module: click_approval_center
        - action: click
          selector: 频道页活动配置表
        - action: wait
        - action: fill
          selector: 配置活动名称搜索
          value: "UI脚本专用001"
        - action: fill
          selector: 配置提审人搜索
          value: "测试店长"
        - action: click
          selector: 配置提审门店搜索
        - action: click
          selector: 配置选中门店
        - action: click
          selector: 配置审批状态搜索
        - action: click
          selector: 配置驳回
        - action: click
          selector: 配置活动位置搜索
        - action: click
          selector: 频道页双列车卡
        - action: click
          selector: 配置查询
        - action: click
          selector: 配置重置
        - action: wait
        - action: assert_value
          selector: 配置活动名称搜索
          value: ""
        - action: assert_value
          selector: 配置提审人搜索
          value: ""
        - action: assert_value
          selector: 配置提审门店搜索
          value: ""
        - action: assert_value
          selector: 配置审批状态搜索
          value: ""
        - action: assert_value
          selector: 配置活动位置搜索
          value: ""









