elements:
  c端运营配置: //span[@title="C端运营配置"]
  图片处理工具: //span[contains(text(),"图片处理工具")]
  运营投放配置: //span[contains(text(),"运营投放配置")]
  车系排序配置: //span[contains(text(),"车系排序配置")]
  车系配置: //span[contains(text(),"车系配置")]
  推荐试驾: //span[contains(text(),"推荐试驾")]
  精彩现场: //span[contains(text(),"精彩现场")]
  城市权益配置: //span[contains(text(),"城市权益配置")]
  优惠权益配置: //span[contains(text(),"优惠权益配置")]
  资源位配置: //span[contains(text(),"资源位配置")]

  # 公共
  重置: (//span[contains(text(),'重 置')])[1]
  查询: //span[contains(text(),"查 询")]
  title: //span[@class="ant-page-header-heading-title"]
  新建: (//span[contains(text(),'新 建')])[1]
  弹框: //div[@role="document"]
  弹框标题: //div[@class="ant-modal-header"]
  弹框关闭: (//button[@aria-label='Close'])[1]
  # 车系排序配置
  order_品牌: (//div[@class='ant-select-selector'])[1]
  order_车系: (//div[@class='ant-select-selector'])[2]
  order_品牌_大众: div[title='大众'] div:nth-child(1)
  order_车系_save: text=${series_name}
  order_tb_品牌: //tbody/tr[1]/td[3]
  order_tb_车系: //tbody/tr[1]/td[5]

  # 试驾推荐
  recommend_组合名称: //input[@id="combinationName"]
  recommend_城市: (//div[@class='ant-select-selector'])[1]
  recommend_状态: (//div[@class='ant-select-selector'])[2]
  recommend_城市_input: //input[@id='cityId']
  recommend_城市_value: (//div[contains(@title,'${recommend_city}')])[1]
  recommend_状态_启用: //div[contains(@title,'开启')]
  recommend_状态_关闭: //div[contains(@title,'关闭')]
  recommend_tb_组合名称: //tbody/tr[2]/td[1]
  recommend_tb_城市: //tbody/tr[2]/td[5]
  recommend_tb_状态: (//button[contains(@role,'switch')])[1]
  recommend_tb_编辑: (//a[contains(text(),'修改')])[1]
  recommend_编辑_名称: //input[@id='basic_groupName']
  recommend_编辑_城市: //div[@id='basic_city']/div
  recommend_编辑_状态: //div[@class='ant-select-selector']

  # resource 资源位配置
  resource_标题: //span[@class="ant-input-affix-wrapper"]/input
  resource_类型: (//div[contains(@class,'ant-form-item-control-input')])[3]
  resource_类型_1: (//div[@class='ant-select-item-option-content'])[1]
  resource_类型_2: //div[@class="rc-virtual-list-holder-inner"]/div[2]
  resource_状态: (//div[contains(@class,'ant-form-item-control-input')])[5]
  resource_状态_启用: //div[@class='ant-select-item-option-content' and normalize-space()='启用']
  resource_状态_禁用: //div[@class='ant-select-item-option-content' and normalize-space()='禁用']
  resource_新建_名称: //input[@id="form_item_name"]
  resource_新建_pvareaid: //input[@role="spinbutton"]
  resource_新建_状态: //div[@class="ant-select ant-select-single ant-select-show-arrow"]
  resource_tb_title: //tbody/tr[1]/td[1]
  resource_tb_pvareaid: //tbody/tr[1]/td[2]
  resource_tb_状态: (//button[@role='switch'])[1]
  resource_tb_编辑: (//button[@type='button' and contains(., '编辑')])[1]
  resource_编辑_类型: (//div[@class="ant-modal-content"]//span[@class='ant-select-selection-item'])[1]
  resource_编辑_状态: (//div[@class="ant-modal-content"]//span[@class='ant-select-selection-item'])[2]

  # 优惠权益
  benefits_位置: (//div[@class='ant-select-selector'])[1]
  benefits_位置_频道页: //div[@label='频道页']
  benefits_权益: (//div[@class='ant-select-selector'])[2]
  benefits_权益_购车礼金: //div[@label='购车礼金']//div[@class='ant-select-item-option-content']
  benefits_品牌: (//div[@class='ant-select-selector'])[3]
  benefits_品牌_Abarth: //div[@class='ant-select-item-option-content'][normalize-space()='Abarth']
  benefits_车系: (//div[@class='ant-select-selector'])[4]
  benefits_车系_Abarth_500新能源: div[label='Abarth 500新能源']
  benefits_城市: (//div[@class='ant-select-selector'])[5]
  benefits_城市_展开: //div[@class='ant-select-tree-list-holder-inner']//div[1]//span[2]
  benefits_城市_北京: //span[@class='ant-select-tree-node-content-wrapper ant-select-tree-node-content-wrapper-normal']//span[@class='ant-select-tree-title'][contains(text(),'北京')]
  benefits_tb_位置: //tbody/tr[1]/td[2]
  benefits_tb_权益: //tbody/tr[1]/td[3]
  benefits_tb_城市: //tbody/tr[1]/td[7]
  benefits_新建活动: //span[contains(text(),'新建活动')]

  # 城市权益配置
  city_rights_标题: (//div[@class="ant-form-item-control-input-content"])[1]
  city_rights_标题_input: //input[@placeholder='请输入标题名称']
  city_rights_类型: (//div[@class="ant-form-item-control-input-content"])[2]
  city_rights_试驾礼: //div[@class='ant-select-item ant-select-item-option ant-select-item-option-active']//div[@class='ant-select-item-option-content']
  city_rights_城市: (//div[@class="ant-form-item-control-input-content"])[3]
  city_rights_城市_展开: //div[@class='ant-select-tree-list-holder-inner']//div[1]//span[2]
  city_rights_城市_北京: //span[@class='ant-select-tree-node-content-wrapper ant-select-tree-node-content-wrapper-normal']
  city_rights_状态: (//div[@class="ant-form-item-control-input-content"])[4]
  city_rights_状态_启用: //div[@class='ant-select-item-option-content'][contains(., '启用')]
  city_rights_状态_关闭: //div[@class='ant-select-item-option-content'][contains(., '关闭')]
  city_rights_tb_类型: //tbody/tr[1]/td[3]
  city_rights_tb_城市: //tbody/tr[1]/td[4]
  city_rights_tb_状态: (//button[@role='switch'])[1]
  city_rights_tb_编辑: (//a[contains(text(),'修改')])[1]
  city_rights_编辑_标题: (//input[@id='basic_equityName'])[1]
  city_rights_编辑_类型: (//div[@class="ant-modal-content"]//span[@class='ant-select-selection-item'])[1]
  city_rights_编辑_状态: (//div[@class="ant-modal-content"]//span[@class='ant-select-selection-item'])[2]

 #精彩现场
  live_标题: //input[@id='topicTitle']
  live_城市: //input[@id='cityNames']
  live_来源: (//div[@class='ant-col ant-form-item-control'])[3]
  live_来源_UGC: //div[@class='ant-select-item-option-content'][normalize-space()='UGC']
  live_启用状态: (//div[@class='ant-col ant-form-item-control'])[4]
  live_启用状态_启用: //div[contains(text(),'启用')]
  live_启用状态_禁用: //div[contains(text(),'禁用')]
  live_tb_来源: //tbody/tr[2]/td[7]
  live_tb_启用状态: (//button[@role='switch'])[1]

  #车系配置
  car_config_位置: (//div[@class='ant-select-selector'])[1]
  car_config_位置_1: (//div[@class='ant-select-item-option-content'])[1]
  car_config_城市: (//div[@class='ant-select-selector'])[2]
  car_config_城市_北京: //div[@class='ant-select-item-option-content'][contains(text(),'北京')]
  car_config_品牌: (//div[@class='ant-select-selector'])[3]
  car_config_品牌_选择: //div[@title='${brand_name}']
  car_config_主推: (//div[@class='ant-select-selector'])[4]
  car_config_主推_是: //div[@class='ant-select-item-option-content'][contains(text(),'是')]
  car_config_主推_否: //div[@class='ant-select-item-option-content'][contains(text(),'否')]
  car_config_类别: (//div[@class='ant-select-selector'])[5]
  car_config_类别_A: //div[@class='ant-select-item-option-content'][normalize-space()='A']
  car_config_启用状态: (//div[@class='ant-select-selector'])[6]
  car_config_启用状态_开启: //div[contains(@title,'开启')]
  car_config_启用状态_关闭: //div[contains(@title,'关闭')]
  car_config_tb_位置: //tbody/tr[3]/td[3]
  car_config_tb_城市: //tbody/tr[3]/td[9]
  car_config_tb_品牌: //tbody/tr[3]/td[4]
  car_config_tb_主推: //tbody/tr[3]/td[7]
  car_config_tb_类别: //tbody/tr[3]/td[6]
  car_config_tb_启用状态: (//button[@role='switch'])[1]

  #运营投放配置
  operation_位置: (//div[@class='ant-select-selector'])[1]
  operation_位置_选项: //div[@title='${dictKey}']
  operation_新增活动: //span[contains(text(),'新增活动')]
  operation_tb_编辑: (//a[contains(text(),'编辑')])[1]
  operation_tb_位置: //tbody/tr[2]/td[7]

  #图片处理工具
  image_storeImage: //input[@id='basic_storeImage']
  image_inputText: (//input[@id='inputText'])[1]
  image_copy: //span[@aria-label='copy']