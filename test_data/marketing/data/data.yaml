test_data:
  test_demo:
    description: "demo测试"
    steps:
      - action: goto
        value: "/ahoh-marketing/ahohIndex4"
      - action: click
        selector: 汽车之家空间站·北京站
      - action: click
        selector: 取消
      - action: click
        selector: 全部
      - action: click
        selector: 万以下
      - action: click
        selector: 全部
      - action: click
        selector: 品牌
      - action: click
        selector: 埃安
      - action: click
        selector: 比亚迪
      - action: click
        selector: 宝马
      - action: click
        selector: 确认选择 (3)
      - action: click
        selector: 轿车
      - action: click
        selector: SUV
      - action: click
        selector: filter-button__icon
      - action: click
        selector: 埃安 比亚迪 宝马
      - action: click
        selector: 清除
      - action: click
        selector: 确认选择

  test_store_info:
    description: "门店信息"
    steps:
      - action: navigate
        value: "/ahoh-marketing/carStore?rimCityId=110100&storeCode=4682"
      - action: assert_text
        selector: 权益slogan
        value: "·买贵赔差价·健康守全家"
      - action: click
        selector: first_image
      - action: store_text
        selector: store_name
        variable_name: store_name
      - action: store_text
        selector: store_label
        variable_name: store_label
      - action: store_text
        selector: store_count_text
        variable_name: store_count_text
      - action: store_text
        selector: worktime_text
        variable_name: worktime_text
      - action: store_text
        selector: car_name
        variable_name: store_car_name
  test_car_detail_click:
    description: "点击进入车系详情"
    steps:
      - action: goto
        value: "/ahoh-marketing/carStore?rimCityId=110100&storeCode=4682"
      - action: click
        selector: car_name
      - action: assert_text
        selector: detail_car_name
        value: ${store_car_name}
      - action: assert_text
        selector: detail_store_title
        value: ${store_name}
      - action: assert_text
        selector: detail_worktime_text
        value: ${worktime_text}
      - action: assert_text
        selector: detail_store_label
        value: ${store_label}
  test_car_detail_goto:
    description: "url进入车系详情"
    steps:
      - action: goto
        value: "/ahoh-marketing/specDetail?seriesId=${series_id}&rimCityId=110100&cityId=110100&pvareaid=6861434&storeCode=4682"
      - action: wait
      - action: assert_text
        selector: detail_car_model
        value: ${car_model}
      - action: assert_text
        selector: detail_car_price
        value: ${car_price}
      - action: assert_text
        selector: detail_item_car_name
        value: ${car_name}
  test_car_detail_gift:
    description: "礼包"
    steps:
      - action: navigate
        value: /ahoh-marketing/specDetail?seriesId=${series_id}&rimCityId=110100&cityId=110100&pvareaid=6861434&storeCode=4682
      - action: assert_text
        selector: detail_gift_title
        value: "1280"
      - action: click
        selector: detail_gift_button
      - action: assert_text
        selector: detail_pop_title
        value: 之家空间站购车礼包
      - action: click
        selector: detail_pop_btn
      - action: assert_text
        selector: toast_text
        value: 请输入预约手机号
      - action: fill
        selector: detail_pop_phone
        value: "15011290391"
      - action: click
        selector: detail_pop_btn
      - action: assert_text
        selector: toast_text
        value: 请勾选用户协议
      - action: assert_text
        selector: detail_pop_phone_style
        value: 已加密处理
      - action: click
        selector: detail_pop_close

  test_detail_store_info:
    description: "门店信息"
    steps:
      - action: navigate
        value: /ahoh-marketing/specDetail?seriesId=${series_id}&rimCityId=110100&cityId=110100&pvareaid=6861434&storeCode=4682
      - action: click
        selector: choice_store_info
      - action: assert_text
        selector: choice_text
        value: 门店信息
      - action: assert_text
        selector: detail_one_store
        value: 导航到店
      - action: assert_text
        selector: detail_call
        value: 电话咨询
      - action: assert_text
        selector: detail_question
        value: 在线咨询

  test_detail_car_clc:
    description: "购车计算"
    steps:
      - action: navigate
        value: /ahoh-marketing/specDetail?seriesId=${series_id}&rimCityId=110100&cityId=110100&pvareaid=6861434&storeCode=4682
      - action: click
        selector: choice_car_clc
      - action: assert_text
        selector: choice_text
        value: 购车计算
      - action: assert_text
        selector: detail_car_purchase
        value: 咨询精细分期方案
      - action: click
        selector: detail_car_purchase
      - action: assert_text
        selector: detail_pop_title
        value: 专业管家为您讲解金融方案
      - action: fill
        selector: detail_pop_phone
        value: "15011290391"
      - action: click
        selector: detail_pop_btn
      - action: assert_text
        selector: toast_text
        value: 请勾选用户协议
      - action: assert_text
        selector: detail_pop_phone_style
        value: 已加密处理
      - action: click
        selector: detail_pop_close
  test_detail_car_trade_in:
    description: "以旧换新"
    steps:
      - action: navigate
        value: /ahoh-marketing/specDetail?seriesId=${series_id}&rimCityId=110100&cityId=110100&pvareaid=6861434&storeCode=4682
      - action: click
        selector: choice_trade_in
      - action: assert_text
        selector: choice_text
        value: 以旧换新
      - action: assert_text
        selector: detail_new_name
        value: ${car_model}
      - action: click
        selector: detail_old_btn
      - action: click
        selector: detail_old_my-car
      - action: store_text
        selector: detail_old_brand_first_brand
        variable_name: 旧品牌
      - action: click
        selector: detail_old_brand_first_brand
      - action: store_text
        selector: detail_car_series_1_series
        variable_name: 旧车系
      - action: click
        selector: detail_car_series_1_series
      - action: store_text
        selector: detail_car_1_car
        variable_name: 旧车型
      - action: click
        selector: detail_car_1_car
      - action: click
        selector: detail_old_address
      - action: store_text
        selector: detail_address_1_hot
        variable_name: 旧城市
      - action: click
        selector: detail_address_1_hot
      - action: store_text
        selector: detail_address_country_1
        variable_name: 旧地区
      - action: click
        selector: detail_address_country_1
      - action: click
        selector: detail_old_time
      - action: click
        selector: detail_time_confirm
      - action: click
        selector: detail_old_mileage
      - action: click
        selector: detail_mileage_confirm
      - action: assert_text
        selector: detail_old_car_text
        value: ${旧车系} ${旧车型}
      - action: assert_text
        selector: detail_old_address_text
        value: ${旧城市}-${旧地区}
      - action: click
        selector: detail_old_confirm
      - action: assert_text
        selector: detail_old_name
        value: ${旧车型}
      - action: click
        selector: detail_old_del
      - action: assert_visible
        selector: detail_old_btn

  test_detail_car_trade_in_pop:
    description: "以旧换新"
    steps:
      - action: navigate
        value: /ahoh-marketing/specDetail?seriesId=${series_id}&rimCityId=110100&cityId=110100&pvareaid=6861434&storeCode=4682
      - action: click
        selector: choice_trade_in
      - action: assert_text
        selector: choice_text
        value: 以旧换新
      - action: click
        selector: detail_old_btn
      - action: wait
      - action: assert_visible
        selector: detail_old_填写车型
      - action: assert_visible
        selector: detail_old_选择地址
      - action: assert_visible
        selector: detail_old_选择时间
      - action: assert_visible
        selector: detail_old_选择里程
      - action: click
        selector: detail_old_close
      - action: assert_visible
        selector: detail_old_btn

  test_detail_car_trade_in_brand:
    description: "以旧换新"
    steps:
      - action: navigate
        value: /ahoh-marketing/specDetail?seriesId=${series_id}&rimCityId=110100&cityId=110100&pvareaid=6861434&storeCode=4682
      - action: click
        selector: choice_trade_in
      - action: assert_text
        selector: choice_text
        value: 以旧换新
      - action: click
        selector: detail_old_btn
      - action: click
        selector: detail_old_my-car
      - action: assert_text
        selector: detail_old_brand_title
        value: 选择旧车品牌
      - action: click
        selector: detail_old_brand_close
      - action: assert_text
        selector: detail_brand_hot
        value: 热门品牌
      - action: click
        selector: detail_old_my-car
      - action: click
        selector: detail_old_brand_D
      - action: click
        selector: detail_old_brand_大众
      - action: assert_text
        selector: detail_old_car_series_title
        value: 选择旧车车系
      - action: assert_text
        selector: detail_car_series_1_car_fac
        value: 一汽-大众
      - action: click
        selector: detail_car_series_back
      - action: wait
      - action: click
        selector: detail_old_brand_大众
      - action: wait
      - action: click
        selector: detail_car_series_2_car_fac
      - action: click
        selector: //span[normalize-space()="Arteon"]
      - action: assert_text
        selector: detail_car_title
        value: 选择旧车车型
      - action: click
        selector: detail_car_back
      - action: wait
      - action: click
        selector: detail_car_series_1_series
      - action: click
        selector: detail_car_1_car








  test_detail_car_trade_quotation:
    description: "以旧换新"
    steps:
      - action: navigate
        value: /ahoh-marketing/specDetail?seriesId=${series_id}&rimCityId=110100&cityId=110100&pvareaid=6861434&storeCode=4682
      - action: click
        selector: choice_trade_in
      - action: assert_text
        selector: choice_text
        value: 以旧换新
      - action: assert_text
        selector: detail_car_trade_in
        value: 咨询精准报价
      - action: click
        selector: detail_car_trade_in
      - action: assert_text
        selector: detail_pop_title
        value: 以旧换新服务
      - action: fill
        selector: detail_pop_phone
        value: "15011290391"
      - action: click
        selector: detail_pop_btn
      - action: assert_text
        selector: toast_text
        value: 请勾选用户协议
      - action: assert_text
        selector: detail_pop_phone_style
        value: 已加密处理
      - action: click
        selector: detail_pop_close
  test_detail_car_test_drive:
    description: "试驾服务"
    steps:
      - action: navigate
        value: /ahoh-marketing/specDetail?seriesId=${series_id}&rimCityId=110100&cityId=110100&pvareaid=6861434&storeCode=4682
      - action: click
        selector: choice_test_drive
      - action: assert_text
        selector: choice_text
        value: 试驾服务
      - action: assert_text
        selector: detail_car_test_drive
        value: 预约对比试驾
      - action: click
        selector: detail_car_test_drive
      - action: assert_text
        selector: detail_pop_title
        value: 一站式多品牌试驾
      - action: fill
        selector: detail_pop_phone
        value: "15011290391"
      - action: click
        selector: detail_pop_btn
      - action: assert_text
        selector: toast_text
        value: 请勾选用户协议
      - action: assert_text
        selector: detail_pop_phone_style
        value: 已加密处理
      - action: click
        selector: detail_pop_close
  test_detail_car_choice:
    description: "选择车型"
    steps:
      - action: navigate
        value: /ahoh-marketing/specDetail?seriesId=${series_id}&rimCityId=110100&cityId=110100&pvareaid=6861434&storeCode=4682
      - action: click
        selector: choice_test_drive
      - action: assert_text
        selector: choice_text
        value: 试驾服务
      - action: click
        selector: detail_add_compare
      - action: assert_text
        selector: detail_choice_car_title
        value: 选择意向车系
      - action: assert_text
        selector: detail_choice_car_name
        value: ${car_name}
      - action: click
        selector: detail_choice_car_clear
      - action: assert_text
        selector: confirm_car_series
        value: 确认选择(1)
      - action: click
        selector: detail_choice_car_choice_1
      - action: click
        selector: detail_choice_car_choice_2
      - action: assert_text
        selector: confirm_car_series
        value: 确认选择(3)
      - action: click
        selector: confirm_car_series
      - action: wait
      - action: assert_element_count
        selector: detail_selected
        value: 2
      - action: click
        selector: detail_selected
      - action: assert_element_count
        selector: detail_selected
        value: 1
  test_detail_floor:
    description: "底部"
    steps:
      - action: navigate
        value: /ahoh-marketing/specDetail?seriesId=82&rimCityId=110100&cityId=110100&pvareaid=6861434&storeCode=4682
      - action: wait
        value: 2
      - action: click
        selector: detail_floor_price
      - action: assert_text
        selector: detail_pop_title
        value: 获取底价
      - action: fill
        selector: detail_pop_phone
        value: "15011290391"
      - action: click
        selector: detail_pop_btn
      - action: assert_text
        selector: toast_text
        value: 请勾选用户协议
      - action: assert_text
        selector: detail_pop_phone_style
        value: 已加密处理
      - action: click
        selector: detail_pop_close
      - action: click
        selector: detail_floor_price
      - action: click
        selector: detail_floor_price_rule
      - action: assert_title
        value: 买贵必赔权益说明
      - action: assert_text
        selector: detail_floor_price_title
        value: "买贵必赔权益说明"

  test_rights_detail:
    description: "权益说明"
    steps:
      - action: navigate
        value: "/ahoh-marketing/carStore?rimCityId=110100&storeCode=4682"
      - action: click
        selector: rights_detail_text
      - action: assert_title
        value: "买贵必赔权益说明"

  test_store_list:
    description: "门店列表"
    steps:
      - action: navigate
        value: "/ahoh-marketing/carStore?rimCityId=110100&storeCode=4682"
      - action: click
        selector: store_count_text
      - action: assert_text
        selector: heading
        value: "之家线下门店"
      - action: click
        selector: get_gift_button
      - action: fill
        selector: phone_textbox
        value: "15011290391"
      - action: click
        selector: car_series_textbox
      - action: click
        selector: second_checkbox
      - action: click
        selector: first_checkbox
      - action: click
        selector: clear_button
      - action: click
        selector: seventh_checkbox
      - action: click
        selector: confirm_selection_button
      - action: click
        selector: phone_textbox
      - action: assert_text
        selector: heading
        value: "领专属礼包"
      - action: click
        selector: car_series_checked
      - action: click
        selector: second_checkbox
      - action: click
        selector: first_checkbox
      - action: click
        selector: third_checkbox
      - action: assert_text
        selector: toast_text
        value: "最多可选3个车系"
      - action: click
        selector: confirm_car_series
      - action: click
        selector: immediately_get_button
      - action: assert_text
        selector: phoneItem-style
        value: "已加密处理"
      - action: assert_text
        selector: toast_text
        value: "请勾选用户协议"
      - action: click
        selector: popup_close_button


  test_warming_gift:
    description: "温馨礼包"
    steps:
      - action: navigate
        value: "/ahoh-marketing/carStore?rimCityId=110100&storeCode=4682"
      - action: click
        selector: third_carousel_image
      - action: click
        selector: immediately_get_button
      - action: assert_text
        selector: get_gift_heading
        value: "领价值1280元暖心礼"
      - action: click
        selector: popup_close_button
  test_inquire_price:
    description: "询价"
    steps:
      - action: navigate
        value: "/ahoh-marketing/carStore?rimCityId=110100&storeCode=4682"
      - action: assert_text
        selector: first_price_button
        value: "询底价"
        nth: 0
      - action: click
        selector: first_price_button
        nth: 0
      - action: assert_text
        selector: inquire_price_heading
        value: "获取底价"
      - action: click
        selector: consult_button
      - action: click
        selector: consult_button
      - action: assert_text
        selector: toast_text
        value: "请输入预约手机号"
      - action: click
        selector: popup_close_button
  test_filter:
    description: "筛选"
    steps:
      - action: navigate
        value: "/ahoh-marketing/carStore?rimCityId=110100&storeCode=4682"
      - action: click
        selector: 品牌
      #      - action: pause
      - action: click
        selector: audi_list_item
      - action: click
        selector: aion_list_item
      - action: click
        selector: bmw_list_item
      - action: assert_text
        selector: brand_list__btn
        value: "确认选择 (3)"
      - action: click
        selector: audi_list_item
      - action: click
        selector: clear_button
      - action: click
        selector: bmw_list_item
      - action: click
        selector: benz_list_item
      - action: click
        selector: brand_list__btn
      - action: assert_text
        selector: brand-num
        value: "2个品牌"
  test_check_level:
    description: 检查等级
    steps:
      - action: goto
        value: /ahoh-marketing/carStore?rimCityId=110100&storeCode=4682
      - action: click
        selector: level
      - action: click
        selector: level_轿车
      - action: monitor_request
        url_pattern: /api/gateway/feed/search/seriesAndSort
        selector: level_btn
        action_type: click
        assert_params:
          $.level: [1]
  test_check_energy:
    description: 检查能源
    steps:
      - action: goto
        value: /ahoh-marketing/carStore?rimCityId=110100&storeCode=4682
      - action: click
        selector: level
      - action: click
        selector: level_纯电动
      - action: click
        selector: level_btn
      - action: click
        selector: first_card
      - action: assert_text
        selector: detail_energy
        expected: 纯电动





