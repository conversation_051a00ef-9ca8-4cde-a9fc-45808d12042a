elements:
  汽车之家空间站·北京站: //div[@class="store-info"]
  取消: text=取消
  全部: text=全部
  万以下: text=万以下
  品牌: text=品牌
  埃安: text=埃安
  比亚迪: text=比亚迪
  宝马: text=宝马
  确认选择 (3): text=确认选择 (3)
  轿车: text=轿车
  SUV: text=SUV
  filter-button__icon: .filter-button__icon
  埃安 比亚迪 宝马: text=埃安 比亚迪 宝马
  清除: text=清除
  确认选择: text=确认选择
  app_container: "#app"
  权益slogan: //body/div[@id='app']/div[1]/div[2]/div[2]
  store_name: div.detail-name > div
  first_image: ".van-image__img"
  store_label: "label"
  navigation_bar: "role=navigation"
  rights_detail_text: "text=权益详情"
  store_count_text: //div[@class='detail-storenum']
  worktime_text: "//div[@class='info-worktime']"
  phone_textbox: "role=textbox[name='手机号码']"
  car_series_textbox: "role=textbox[name='意向车系']"
  car_series_checked: (//span[@class='select-series--value'])[1]
  second_checkbox: (//div[@class='check-btn'])[2]
  first_checkbox: ".check-btn"
  clear_button: "text=清除"
  seventh_checkbox: (//div[@class='check-btn'])[7]
  confirm_selection_button: "text=确认选择(1)"
  selected_2_brands: "text=已选2个品牌"
  heading: "role=heading"
  store_name_text: "text=汽车之家空间站·北京平谷车卖场官方"
  phoneItem-style: //span[@class='phoneItem-style']
  dialog: "role=dialog"
  third_checkbox: (//div[@class='check-btn'])[3]
  confirm_car_series: //div[@class="select-series-box__btnbox"]
  popup_close_button: ".fill-info-popup-close"
  third_carousel_image: "div:nth-child(3) > .carousel-img"
  get_gift_heading: "role=heading"
  first_price_button: (//span[@class='btn'][contains(text(),'询底价')])[1]
  inquire_price_heading: "role=heading"
  consult_button: "role=button[name='立即咨询']"
  reservation_phone_text: "text=请输入预约手机号"
  card_image: ".cardimg > img"
  first_filter_button: ".filter-button__icon"
  audi_list_item: "role=listitem[name='奥迪']"
  audi_list_item_div: "role=listitem[name='奥迪'] > div"
  aion_list_item: "role=listitem[name='埃安']"
  aion_list_item_div: "role=listitem[name='埃安'] > div"
  bmw_list_item: "role=listitem[name='宝马']"
  bmw_list_item_div: "role=listitem[name='宝马'] > div"
  benz_list_item: "role=listitem[name='奔驰']"
  benz_list_item_div: "role=listitem[name='奔驰'] > div"
  brand_list__btn: //div[@class="brand-list__btn"]
  get_gift_button: "text=领好礼"
  immediately_get_button: "role=button[name='立即领取']"
  image_role: "role=img"
  confirm_3_text: "text=确认选择 (3)"
  toast_text: "div.van-toast__text"
  brand-num: //span[@class="checked-brand-num"]
  car_name: //div[@class="series-card__name"]/span[1]
  level: //span[@class="van-popover__wrapper"]//div[@class="filter-button__container"]
  level_轿车: //li[contains(text(),'轿车')]
  level_纯电动: //li[contains(text(),'纯电动')]
  level_btn:  //div[@class="btn"]
  first_card: (//div[@class="series-card"])[1]
  detail_energy: (//div[@class="column-body"])[2]/div[4]

  ## detail page
  detail_store_title: //div[@class="title"]
  detail_worktime_text: //div[@class="online-time"]
  detail_store_label: //div[@class="bot"]
  detail_car_name: //div[@class="spec-select__name"]
  detail_car_model: //div[@class="car-model-info-name"]
  detail_car_price: //div[@class="activity-price__endprice"]
  detail_item_car_name: //div[@class="compare-test-drive__spec"]/div/div[@class="item-name"]
  detail_gift_title: //h3[@class="gift-packs-cont-title"]/b
  detail_gift_button: //div[@class="gift-packs-cont-right"]

  detail_pop_title: //h2[@class="fill-info-popup-header-title"]
  detail_pop_btn: div.fill-info-form-btn
  detail_pop_phone: "#sunInput"
  detail_pop_phone_style: span.phoneItem-style
  detail_pop_close: div.fill-info-popup-close

  choice_text: //div[@aria-selected="true"]/span
  choice_store_info: //span[contains(text(),"门店信息")]
  choice_car_clc: //span[contains(text(),"购车计算")]
  choice_trade_in: //span[contains(text(),"以旧换新")]
  choice_test_drive: //span[contains(text(),"试驾服务")]


  detail_one_store: //div[@class="oneStore"]
  detail_call: //div[@class="call"]
  detail_question: //div[@class="question"]

  detail_car_purchase: .car-purchase-plan__btn
  detail_car_trade_in: //button[@class='van-button van-button--default van-button--normal change-new-btn']//div[@class='van-button__content']
  detail_car_test_drive: .compare-test-drive__btn
  detail_new_name: .change-new-main-new-name



  detail_old_btn: .change-new-main-old-btn
  detail_old_del: (//div[@class='van-button__content'])[1]
  detail_old_name: //div[@class="change-new-main-old-name"]
  detail_old_my-car: //div[@class="old-info-selectFrom"]//div[1]
  detail_old_address: //div[@class="old-info-selectFrom"]//div[2]
  detail_old_time: //div[@class="old-info-selectFrom"]//div[3]
  detail_old_mileage: //div[@class="old-info-selectFrom"]//div[4]
  detail_old_confirm: //span[contains(text(),"免费估值")]
  detail_old_close: .common-popup-close
  detail_old_car_text: (//p[@class='cont-right'])[1]/span
  detail_old_address_text: (//p[@class='cont-right'])[2]/span
  detail_old_time_text: (//p[@class='cont-right'])[3]/span
  detail_old_mileage_text: (//p[@class='cont-right'])[4]/span

  detail_old_填写车型: //span[contains(text(),"填写车型")]
  detail_old_选择地址: //span[contains(text(),"请选择")]
  detail_old_选择时间: //span[contains(text(),"填写时间")]
  detail_old_选择里程: //span[contains(text(),"填写里程")]

  detail_old_brand_close: (//i[@class='v-athm-auto-icon__close van-icon van-icon-cross'])[1]
  detail_old_car-series_close: (//i[@class='v-athm-auto-icon__close van-icon van-icon-cross'])[1]
  detail_old_car_close: (//i[@class='v-athm-auto-icon__close van-icon van-icon-cross'])[2]
  detail_old_brand_title: (//header[@class='v-athm-auto__header'])[1]//span
  detail_old_car-series_title: (//header[@class='v-athm-auto__header'])[1]//span
  detail_old_car_title: (//header[@class='v-athm-auto__header'])[1]//span

  detail_old_brand_first_brand: //ul[@class="ul-hot"]//li[1]
  detail_old_brand_D: //li[normalize-space()="D"]
  detail_old_brand_大众: //ul[@class="ul-base"]//span[contains(text(),"大众")]
  detail_brand_hot: (//div[@id='athm-jump-a'])[1]

  detail_old_car_series_title: //section[@class="v-athm-auto v-athm-auto-second v-athm-auto-series show"]//header[@class="v-athm-auto__header"]/span
  detail_car_series_1_car_fac: //div[@class="van-tab van-tab--active"]
  detail_car_series_2_car_fac: (//div[@role='tab'])[6]
  detail_car_series_1_series: //section[@class="v-athm-auto v-athm-auto-second v-athm-auto-series show"]//ul[1]//li[1]
  detail_car_series_back: //section[@class="v-athm-auto v-athm-auto-second v-athm-auto-series show"]//i[@class="v-athm-auto-icon__back van-icon van-icon-down"]
  detail_car_series_close: //section[@class="v-athm-auto v-athm-auto-second v-athm-auto-series show"]//i[@class="v-athm-auto-icon__close van-icon van-icon-cross"]
  detail_car_1_car: //section[@class="v-athm-auto v-athm-auto-third show"]//ul[1]//li[1]
  detail_car_title: //section[@class="v-athm-auto v-athm-auto-third show"]//header[@class="v-athm-auto__header"]/span
  detail_car_back: //section[@class="v-athm-auto v-athm-auto-third show"]//i[@class="v-athm-auto-icon__back van-icon van-icon-down"]
  detail_car_close: //section[@class="v-athm-auto v-athm-auto-third show"]//i[@class="v-athm-auto-icon__close van-icon van-icon-cross"]


  detail_address_title: //div[@class="van-nav-bar__title van-ellipsis"]
  detail_address_close: //div[@class="van-nav-bar__right"]
  detail_address_1_hot: (//div[@class='van-cell'])[1]

  detail_address_city_back: .van-nav-bar__left
  detail_address_city_title: (//div[@class="van-nav-bar__title van-ellipsis"])[1]
  detail_address_city_close: (//div[@class='van-nav-bar__right'])[1]
  detail_address-country_title: //div[@class="pop-choose-county van-popup van-popup--round van-popup--right-bottom"]//div[@class="van-nav-bar__content"]//div[1]
  detail_address-country_close: (//div[@class='van-nav-bar__right'])[2]
  detail_address_country_back: (//div[@class='van-nav-bar__left'])[1]
  detail_address_country_1: ((//div[@class='index-barwrap'])[2]//div[@class="van-cell__title"])[1]
  detail_time_pop_time: div[class="licensing-time-pop van-popup van-popup--round van-popup--bottom"] div[class="van-nav-bar__title van-ellipsis"]
  detail_time_pop_close: (//i[@class='van-icon van-icon-cross'])[1]
  detail_time_confirm: (//div[@class='pop-bottom-btn'])[1]

  detail_mileage_title: (//div[@class='van-nav-bar__content'])[2]/div[1]
  detail_mileage_close: (//div[@class='van-nav-bar__right'])[2]
  detail_mileage_confirm: (//div[@class='pop-bottom-btn'])[2]
  detail_del_old: (//div[@class='van-button__content'])[1]
  detail_new_price: //div[@class="change-new-main-price-title"]

  detail_add_compare: .add
  detail_choice_car_clear: .select-series-box__selected--clear
  detail_choice_car_title: .select-series-box__head
  detail_choice_car_name: .not-remove
  detail_choice_car_choice_1: (//div[@class='check-btn'])[1]
  detail_choice_car_choice_2: (//div[@class='check-btn'])[2]
  detail_selected: (//div[@class='item-check active'])

  detail_floor_price: //div[@class="footer__right"]//div[2]
  detail_floor_price_rule: //div[@class="base-price"]//div[contains(text(),"规则")]
  detail_floor_price_title: .MHeader_title__CwNZs