test_data:
  test_demo:
    description: "demo测试"
    steps:
      - action: goto
        value: "https://ahohcrm.autohome.com.cn/#/login"
      - action: click
        selector: "账号密码登录tab"
      - action: click
        selector: role=textbox[name="请输入账号"]
      - action: fill
        selector: role=textbox[name="请输入账号"]
        value: "12500000000"
      - action: click
        selector: role=textbox[name="请输入密码"]
      - action: fill
        selector: role=textbox[name="请输入密码"]
        value: "Admin123!"
      - action: click
        selector: "登录按钮"
      #- action: wait_for_new_window

#添加购车政策
  test_policy_add:
    description: "demo测试"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 购车政策列表
      - action: click
        selector: 新增按钮
      - action: click
        selector: 品牌下拉框
      - action: fill
        selector: 品牌下拉框
        value: "奥迪"
      - action: click
        selector: 奥迪品牌
      - action: click
        selector: 车系列表
      - action: fill
        selector: 车系列表
        value: "A4"
      - action: click
        selector: 奥迪A4L车系
      - action: click
        selector: 车型列表
      - action: click
        selector: 款200万辆悦享版40TFSI时尚动感型
      - action: click
        selector: 经销商省份
      - action: fill
        selector: 经销商省份
        value: "香港"
      - action: click
        selector: 香港省份
      - action: click
        selector: 经销商城市
      - action: click
        selector: 香港城市
      - action: click
        selector: 经销商区域
      - action: click
        selector: 香港区域
      - action: click
        selector: 经销商名称
      - action: click
        selector: 测试宝骏经销商
      - action: click
        selector: 库存状态
      - action: click
        selector: 现车标签
      - action: click
        selector: 交货日期
      - action: click
        selector: 一周交货
      - action: click
        selector: 裸车价输入框
      - action: fill
        selector: 裸车价输入框
        value: "28.780"
      - action: click
        selector: DCC报价输入框
      - action: fill
        selector: DCC报价输入框
        value: "28.780"
      - action: click
        selector: 强制保险否
      - action: click
        selector: 强制上牌否
      - action: click
        selector: 本品本店置换
      - action: fill
        selector: 本品本店置换
        value: "100"
      - action: click
        selector: 形式
      - action: click
        selector: 抵扣车款
      - action: click
        selector: 添加按钮
      - action: click
        selector: 比例模式
      - action: click
        selector: 首付比例
      - action: fill
        selector: 首付比例
        value: "10"
      - action: click
        selector: 免息期数
      - action: click
        selector: 12期
      - action: click
        selector: 低首付金融
      - action: fill
        selector: 低首付金融
        value: "13"
      - action: click
        selector: 金融手续费
      - action: fill
        selector: 金融手续费
        value: "1.000"
      - action: click
        selector: 金融购车推荐方案
      - action: fill
        selector: 金融购车推荐方案
        value: "方案1添加"
      - action: click
        selector: 无绑定条件
      - action: click
        selector: 无特殊颜色
      - action: click
        selector: 提交按钮
      - action: click
        selector: 查询按钮
      - use_module: list_add_data_steps

#停用政策
  test_policy_deactivate:
    description: "demo测试"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 购车政策列表
      - use_module: filter_steps
      - action: click
        selector: 停用按钮
      - action: click
        selector: 确认按钮
      - action: click
        selector: 启用状态筛选项
      - action: click
        selector: 停用选项
      - action: click
        selector: 查询按钮
      - action: assert_text
        selector: 第一行
        value: 2025款 200万辆悦享版 40 TFSI 时尚动感型
      - action: assert_text
        selector: 状态
        value: 启用

#新建详情
  test_policy_details:
    description: "demo测试"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 购车政策列表
      - use_module: filter_steps
      - action: click
        selector: 编辑按钮
      - action: assert_text
        selector: 新建详情品牌
        value: "奥迪"
      - action: assert_text
        selector: 新建详情车系
        value: 奥迪A4L
      - action: assert_text
        selector: 新建详情车型
        value: 2025款 200万辆悦享版 40 TFSI 时尚动感型
      - action: assert_text
        selector: 新建详情经销商省份
        value: 香港
      - action: assert_text
        selector: 新建详情经销商城市
        value: 香港
      - action: assert_text
        selector: 新建详情经销商区域
        value: 香港
      - action: assert_text
        selector: 新建详情经销商名称
        value: 测试宝骏20150609
      - action: assert_text
        selector: 新建详情库存状态
        value: 现车
      - action: assert_text
        selector: 新建详情交货日期
        value: 1周
      - action: 验证值
        selector: 新建详情DCC报价
        value: "28.780"
      - action: 验证值
        selector: 新建详情裸车价
        value: "28.780"
      - action: assert_text
        selector: 强制保险否
        value: 否
      - action: assert_text
        selector: 强制上牌否
        value: 否
      - action: 验证值
        selector: 新建详情本品本店置换
        value: "100"
      - action: assert_text
        selector: 新建详情比例模式
        value: 比例模式
      - action: assert_text
        selector: 新建详情形式
        value: 抵扣车款
      - action: 验证值
        selector: 新建详情首付比例
        value: "10"
      - action: assert_text
        selector: 新建详情免息期数
        value: "12期"
      - action: 验证值
        selector: 新建详情低首付金融
        value: "13"
      - action: 验证值
        selector: 新建详情金融手续费
        value: "1.000"
      - action: 验证值
        selector: 新建详情金融购车推荐方案
        value: 方案1添加
      - action: assert_text
        selector: 无绑定条件
        value: 无
      - action: assert_text
        selector: 无特殊颜色
        value: 无
      - action: assert_text
        selector: 新建详情提交按钮
        value: 提 交
      - action: assert_text
        selector: 新建详情取消按钮
        value: 取 消

#删除政策
  test_policy_delete:
    description: "demo测试"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 购车政策列表
      - use_module: filter_steps
      - action: click
        selector: 删除按钮
      - action: click
        selector: 确认删除
      - action: click
        selector: 查询按钮
      - action: assert_text
        selector: 政策暂无数据
        value: 暂无数据

#筛选
  test_policy_filter:
    description: "demo测试"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 购车政策列表
      - use_module: filter_steps
      - action: click
        selector: 查询按钮
      - action: assert_text
        selector: 新建列表数据奥迪
        value: 奥迪
      - action: assert_text
        selector: 新建列表数据奥迪A4L
        value: 奥迪A4L
      - action: assert_text
        selector: 新建列表数据车型
        value: 2025款 200万辆悦享版 40 TFSI 时尚动感型
      - action: assert_text
        selector: 新建列表数据香港
        value: 香港

#编辑政策
  test_policy_edit:
    description: "demo测试"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 购车政策列表
      - use_module: filter_steps
      - action: click
        selector: 编辑按钮
      - action: click
        selector: 编辑库存状态输入框
      - action: click
        selector: 预定标签
      - action: click
        selector: 编辑交货日期输入框
      - action: click
        selector: 3月以上交货日期
      - action: click
        selector: 裸车价输入框
      - action: clear
        selector: 裸车价输入框
      - action: fill
        selector: 裸车价输入框
        value: "28.879"
      - action: click
        selector: DCC报价输入框
      - action: clear
        selector: DCC报价输入框
      - action: fill
        selector: DCC报价输入框
        value: "28.880"
      - action: click
        selector: 强制保险是
      - action: click
        selector: 强制上牌是
      - action: click
        selector: 上牌费输入框
      - action: fill
        selector: 上牌费输入框
        value: "0.400"
      - action: click
        selector: 本品本店置换
      - action: fill
        selector: 本品本店置换
        value: "200"
      - action: click
        selector: 编辑形式输入框
      - action: click
        selector: 单独兑现
      - action: click
        selector: 删除金融方案
      - action: click
        selector: 低首付金融
      - action: clear
        selector: 低首付金融
      - action: fill
        selector: 低首付金融
        value: "15"
      - action: click
        selector: 金融手续费
      - action: clear
        selector: 金融手续费
      - action: fill
        selector: 金融手续费
        value: "0.900"
      - action: click
        selector: 金融购车推荐方案
      - action: clear
        selector: 金融购车推荐方案
      - action: click
        selector: 有绑定条件
      - action: click
        selector: 有特殊颜色
      - action: click
        selector: 颜色选择框
      - action: click
        selector: 阿科纳白
      - action: click
        selector: 颜色价格输入框
      - action: fill
        selector: 颜色价格输入框
        value: "1.000"
      - action: click
        selector: 提交按钮
      - action: assert_text
        selector: 编辑列表数据库存状态列
        value: "预订"

#重置筛选
  test_policy_reset:
    description: "demo测试"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 购车政策列表
      - use_module: filter_steps
      - action: click
        selector: 重置按钮
      - action: assert_text
        selector: 重置后经销商省份筛选框为默认值
        value: "请选择经销商省份"
      - action: assert_text
        selector: 列表第二行数据序号
        value: "2"

#政策列表
  test_policy_list:
    description: "demo测试"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 购车政策列表
      - use_module: filter_steps
      - action: click
        selector: 购车政策
      - action: assert_text
        selector: cell_1
        value: "1"
      - action: assert_text
        selector: text_audi
        value: "奥迪"
      - action: assert_text
        selector: text_a4l
        value: "奥迪A4L"
      - action: assert_text
        selector: text_version
        value: "2025款 200万辆悦享版 40 TFSI 时尚动感型"
      - action: click
        selector: cell_hk
        value: "香港"
      - action: assert_text
        selector: cell_test
        value: "测试宝骏20150609"
      - action: assert_text
        selector: cell_28_980
        value: "28.980"
      - action: assert_text
        selector: cell_booking
        value: "预订"
      - action: assert_text
        selector: cell_month
        value: "3个月以上"
      - action: assert_text
        selector: 裸车价cell_28_880
        value: "28.879"
      - action: assert_text
        selector: 线上报价cell_28_880
        value: "28.880"
      - action: assert_text
        selector: text_white
        value: "阿科纳白"
      - action: assert_text
        selector: text_1_000
        value: "1.000"
      - action: assert_text
        selector: 强制保险cell_force
        value: "强制"
      - action: assert_text
        selector: 强制上牌cell_force
        value: "强制"
      - action: assert_text
        selector: cell_0_400
        value: "0.400"
      - action: assert_text
        selector: cell_0_900
        value: "0.900"

