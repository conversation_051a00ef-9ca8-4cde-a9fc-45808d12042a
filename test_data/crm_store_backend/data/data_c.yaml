test_data:
  test_series_add:
    description: "新增车系"
    steps:
      - use_module: login_steps
      - action: click
        selector: 车系配置
      - action: assert_url
        value: https://ahohcrm.autohome.com.cn/#/carSeriesConfig
      - action: click
        selector: 新建
      - action: click
        selector: 点击查看/修改
      - action: wait
      - action: click
        selector: 展开
      - action: click
        selector: 奥迪A8
      - action: click
        selector: 右移
      - action: click
        selector: 确定
      - action: click
        selector: 品牌类别C
      - action: click
        selector: 直推车否
      - action: fill
        selector: 序号
        value: "6"

  test_series_update:
    description: "修改车系"
    steps:
      - use_module: login_steps
      - action: click
        selector: 车系配置
      - action: fill
        selector: 筛选车系
        value: "奥迪A6L"
      - action: click
        selector: 查询
      - action: click
        selector: 修改
      - action: click
        selector: 点击查看/修改
      - action: click
        selector: 已选中
      - action: click
        selector: 左移
      - action: click
        selector: 展开
      - action: click
        selector: 奥迪A6
      - action: click
        selector: 右移
      - action: click
        selector: 确定

  test_series_screen:
    description: "筛选车系"
    steps:
      - use_module: login_steps
      - action: click
        selector: 车系配置
      - action: click
        selector: 位置下拉框
      - action: click
        selector: 频道页车源列表位置
      - action: store_text
        selector: 频道页车源列表位置
        variable_name: 频道页车源列表位置
      - action: fill
        selector: 车系输入框
        value: "奥迪A6L"
      - action: click
        selector: 品牌
      - action: click
        selector: 品牌筛选
      - action: store_text
        selector: 品牌筛选
        variable_name: 品牌筛选
      - action: click
        selector: 主推筛选
      - action: click
        selector: 是主推
      - action: store_text
        selector: 是主推
        variable_name: 是主推
      - action: click
        selector: 品牌类别
      - action: click
        selector: 品牌类别筛选C
      - action: store_text
        selector: 品牌类别筛选C
        variable_name: 品牌类别筛选C
      - action: click
        selector: 启用状态
      - action: click
        selector: 启用
      - action: click
        selector: 筛选查询
      - action: assert_text
        selector: 列表位置
        value: ${频道页车源列表位置}
      - action: assert_text
        selector: 列表品牌
        value: ${品牌筛选}
      - action: assert_text
        selector: 列表车系
        value: "奥迪A6L"
      - action: assert_text
        selector: 列表类别
        value: ${品牌类别筛选C}
      - action: assert_text
        selector: 列表主推
        value: ${是主推}

  test_screen_reset:
    description: "筛选重置"
    steps:
      - use_module: login_steps
      - action: click
        selector: 车系配置
      - action: click
        selector: 位置下拉框
      - action: click
        selector: 频道页车源列表位置
      - action: fill
        selector: 车系输入框
        value: "奥迪A6L"
      - action: assert_value
        selector: 车系输入框
        value: "奥迪A6L"
      - action: click
        selector: 品牌
      - action: click
        selector: 品牌筛选
      - action: click
        selector: 主推筛选
      - action: click
        selector: 是主推
      - action: click
        selector: 品牌类别
      - action: click
        selector: 品牌类别筛选C
      - action: click
        selector: 启用状态
      - action: click
        selector: 启用
      - action: click
        selector: 车系重置
      - action: assert_value
        selector: 位置下拉框
        value: ""
      - action: assert_value
        selector: 车系输入框
        value: ""
      - action: assert_value
        selector: 品牌
        value: ""
      - action: assert_value
        selector: 主推筛选
        value: ""
      - action: assert_value
        selector: 品牌类别
        value: ""
      - action: assert_value
        selector: 启用状态
        value: ""

  test_new_equity:
    description: "新增权益"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 优惠权益配置
      - action: click
        selector: 新建活动
      - action: click
        selector: 位置选择
      - action: click
        selector: 频道页双列车卡
      - action: click
        selector: 权益类型
      - action: click
        selector: 到店礼
      - action: fill
        selector: 价值
        value: "1500"
      - action: fill
        selector: 标题
        value: "自动化测试001"
      - action: click
        selector: 开始时间
      - action: click
        selector: 年份选择
      - action: click
        selector: 初始时间
      - action: click
        selector: 30年代
      - action: click
        selector: 2030年
      - action: click
        selector: 月份选择
      - action: click
        selector: 2030-01-01
      - action: click
        selector: 起止时间确定
      - action: click
        selector: 结束时间
      - action: click
        selector: 2030-01-06
      - action: click
        selector: 起止时间确定
      - action: click
        selector: 权益配置确定
      - action: wait

  test_screen_equity:
    description: "权益筛选"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 优惠权益配置
      - action: click
        selector: 权益位置下拉框
      - action: click
        selector: 频道页双列车卡选项
      - action: click
        selector: 权益类型下拉框
      - action: click
        selector: 到店礼选项
      - action: fill
        selector: 权益标题
        value: "自动化测试001"
      - action: click
        selector: 权益查询
      - action: wait
      - action: assert_text
        selector: 查询结果标题
        value: "自动化测试001"

  test_screen_equity_reset:
    description: "权益筛选重置"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 优惠权益配置
      - action: click
        selector: 权益位置下拉框
      - action: click
        selector: 频道页双列车卡选项
      - action: click
        selector: 权益类型下拉框
      - action: click
        selector: 到店礼选项
      - action: fill
        selector: 权益标题
        value: "测试002"
      - action: click
        selector: 权益查询
      - action: click
        selector: 权益重置
      - action: assert_value
        selector: 权益位置下拉框
        value: ""
      - action: assert_value
        selector: 权益类型下拉框
        value: ""
      - action: assert_value
        selector: 权益标题
        value: ""

  test_del_equity:
    description: "权益删除"
    steps:
      - use_module: login_steps
      - action: click
        selector: 购车政策
      - action: click
        selector: 优惠权益配置
      - action: fill
        selector: 权益标题
        value: "自动化测试001"
      - action: click
        selector: 权益查询
      - action: wait
      - action: click
        selector: 删除
      - action: click
        selector: 取消
      - action: click
        selector: 删除
      - action: click
        selector: 确认
      - action: wait
      - action: assert_text
        selector: 暂无数据
        value: "暂无数据"












