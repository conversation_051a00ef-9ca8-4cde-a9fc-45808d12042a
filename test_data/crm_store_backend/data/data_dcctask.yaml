test_data:
  test_dcc_task_search:
    description: "线索&工单管理-主任务列表-组合搜索1"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 主任务列表
      - action: click
        selector: 主任务列表筛选手机号输入框
      - action: fill
        selector: 主任务列表筛选手机号输入框
        value: "15233224950"
      - action: press_key
        selector: 主任务列表筛选手机号输入框
        value: Enter
      - action: click
        selector: 主任务列表筛选任务创建日期开始
      - action: fill
        selector: 主任务列表筛选任务创建日期开始
        value: "2024-09-21"
      - action: press_key
        selector: 主任务列表筛选任务创建日期开始
        value: Enter
      - action: wait
        value: 1
      - action: click
        selector: 主任务列表筛选任务创建日期结束
      - action: fill
        selector: 主任务列表筛选任务创建日期结束
        value: "2024-09-21"
      - action: press_key
        selector: 主任务列表筛选任务创建日期结束
        value: Enter
      - action: click
        selector: 主任务列表筛选分配日期开始
      - action: fill
        selector: 主任务列表筛选分配日期开始
        value: "2024-09-21"
      - action: press_key
        selector: 主任务列表筛选分配日期开始
        value: Enter
      - action: wait
        value: 1
      - action: click
        selector: 主任务列表筛选分配日期结束
      - action: fill
        selector: 主任务列表筛选分配日期结束
        value: "2024-09-21"
      - action: press_key
        selector: 主任务列表筛选分配日期结束
        value: Enter
      - action: click
        selector: 主任务列表筛选计划联系日期开始
      - action: fill
        selector: 主任务列表筛选计划联系日期开始
        value: "2024-09-21"
      - action: press_key
        selector: 主任务列表筛选计划联系日期开始
        value: Enter
      - action: click
        selector: 主任务列表筛选计划联系日期结束
      - action: fill
        selector: 主任务列表筛选计划联系日期结束
        value: "2024-09-21"
      - action: press_key
        selector: 主任务列表筛选计划联系日期结束
        value: Enter
      - action: click
        selector: 主任务列表筛选实际完成日期开始
      - action: fill
        selector: 主任务列表筛选实际完成日期开始
        value: "2024-09-21"
      - action: press_key
        selector: 主任务列表筛选实际完成日期开始
        value: Enter
      - action: click
        selector: 主任务列表筛选实际完成日期结束
      - action: fill
        selector: 主任务列表筛选实际完成日期结束
        value: "2024-09-21"
      - action: press_key
        selector: 主任务列表筛选实际完成日期结束
        value: Enter
      - action: click
        selector: 主任务列表筛选创建集团
      - action: fill
        selector: 主任务列表筛选创建集团
        value: "新零售加盟商测试租户"
      - action: press_key
        selector: 主任务列表筛选创建集团
        value: Enter
      - action: click
        selector: 主任务列表筛选跟进人
      - action: fill
        selector: 主任务列表筛选跟进人
        value: "管家2"
      - action: press_key
        selector: 主任务列表筛选跟进人
        value: Enter
      - action: click
        selector: 主任务列表筛选主任务状态
      - action: fill
        selector: 主任务列表筛选主任务状态
        value: "待清洗"
      - action: press_key
        selector: 主任务列表筛选主任务状态
        value: Enter
      - action: click
        selector: 主任务列表筛选主任务类型
      - action: fill
        selector: 主任务列表筛选主任务类型
        value: "已完成"
      - action: press_key
        selector: 主任务列表筛选主任务类型
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 主任务列表组合筛选验证
        value: "22520906127532"

  test_dcc_task_list:
    description: "线索&工单管理-主任务列表-列表验证"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 主任务列表
      - action: click
        selector: 主任务列表筛选线索id输入框
      - action: fill
        selector: 主任务列表筛选线索id输入框
        value: "22520906127532"
      - action: press_key
        selector: 主任务列表筛选线索id输入框
        value: Enter
      - action: click
        selector: 主任务列表筛选主任务类型
      - action: fill
        selector: 主任务列表筛选主任务类型
        value: "待清洗"
      - action: press_key
        selector: 主任务列表筛选主任务类型
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 主任务列表组合筛选验证
        value: "22520906127532"
      - action: assert_text
        selector: 主任务列表列表姓名验证
        value: "ces"
      - action: assert_text
        selector: 主任务列表列表手机号验证
        value: "152****4950"
      - action: assert_text
        selector: 主任务列表列表线索状态验证
        value: "完成"
      - action: assert_text
        selector: 主任务列表列表来源验证
        value: "新零售-手工录入-自然进店-自然进店"
      - action: assert_text
        selector: 主任务列表列表主任务id验证
        value: "22520906127788"
      - action: assert_text
        selector: 主任务列表列表任务类型验证
        value: "待清洗"
      - action: assert_text
        selector: 主任务列表列表主任务状态验证
        value: "已完成"
      - action: assert_text
        selector: 主任务列表列表创建时间验证
        value: "2024-09-21 16:00:39"
      - action: assert_text
        selector: 主任务列表列表分配时间验证
        value: "2024-09-21 16:00:39"
      - action: assert_text
        selector: 主任务列表列表计划时间验证
        value: "2024-09-21 22:00:00"
      - action: assert_text
        selector: 主任务列表列表逾期时间验证
        value: "2024-09-21 22:00:00"
      - action: assert_text
        selector: 主任务列表列表实际完成时间验证
        value: "2024-09-21 16:02:13"
      - action: assert_text
        selector: 主任务列表列表集团验证
        value: "新零售加盟商测试租户"
      - action: assert_text
        selector: 主任务列表列表门店验证
        value: "加盟商香港综合测试门店1"
      - action: assert_text
        selector: 主任务列表列表员工验证
        value: "管家2"
      - action: assert_text
        selector: 主任务列表列表跟进人验证
        value: "管家2"

  test_dcc_task_search_status:
    description: "线索&工单管理-线索管理-各任务状态搜索"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 主任务列表
      - action: click
        selector: 主任务列表筛选主任务状态
      - action: fill
        selector: 主任务列表筛选主任务状态
        value: "服务中"
      - action: press_key
        selector: 主任务列表筛选主任务状态
        value: Enter
      - action: click
        selector: 主任务列表筛选查询按钮
      - action: wait
        value: 2
      - action: assert_text
        selector: 主任务列表列表主任务状态服务中验证
        value: "服务中"
      - action: refresh
      - action: click
        selector: 主任务列表筛选主任务状态
      - action: fill
        selector: 主任务列表筛选主任务状态
        value: "已完成"
      - action: press_key
        selector: 主任务列表筛选主任务状态
        value: Enter
      - action: click
        selector: 主任务列表筛选查询按钮
      - action: wait
        value: 2
      - action: assert_text
        selector: 主任务列表列表主任务状态验证
        value: "已完成"
      - action: refresh
      - action: click
        selector: 主任务列表筛选主任务状态
      - action: fill
        selector: 主任务列表筛选主任务状态
        value: "已取消"
      - action: press_key
        selector: 主任务列表筛选主任务状态
        value: Enter
      - action: click
        selector: 主任务列表筛选查询按钮
      - action: wait
        value: 2
      - action: assert_text
        selector: 主任务列表列表主任务状态验证
        value: "已取消"


  test_dcc_task_search_type:
    description: "线索&工单管理-线索管理-各任务类型搜索"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 主任务列表
      - action: click
        selector: 主任务列表筛选主任务类型
      - action: fill
        selector: 主任务列表筛选主任务类型
        value: "待清洗"
      - action: press_key
        selector: 主任务列表筛选主任务类型
        value: Enter
      - action: click
        selector: 主任务列表筛选查询按钮
      - action: wait
        value: 2
      - action: assert_text
        selector: 主任务列表列表任务类型验证
        value: "待清洗"
      - action: refresh
      - action: click
        selector: 主任务列表筛选主任务类型
      - action: fill
        selector: 主任务列表筛选主任务类型
        value: "待回电"
      - action: press_key
        selector: 主任务列表筛选主任务类型
        value: Enter
      - action: wait
        value: 2
      - action: assert_text
        selector: 主任务列表列表任务类型待回电验证
        value: "待回电"
      - action: refresh
      - action: click
        selector: 主任务列表筛选主任务类型
      - action: fill
        selector: 主任务列表筛选主任务类型
        value: "持续跟进"
      - action: press_key
        selector: 主任务列表筛选主任务类型
        value: Enter
      - action: click
        selector: 主任务列表筛选查询按钮
      - action: wait
        value: 2
      - action: assert_text
        selector: 主任务列表列表任务类型持续跟进验证
        value: "持续跟进"
      - action: refresh
      - action: click
        selector: 主任务列表筛选主任务类型
      - action: fill
        selector: 主任务列表筛选主任务类型
        value: "待接待"
      - action: press_key
        selector: 主任务列表筛选主任务类型
        value: Enter
      - action: click
        selector: 主任务列表筛选查询按钮
      - action: wait
        value: 2
      - action: assert_text
        selector: 主任务列表列表任务类型待接待验证
        value: "待接待"

  test_dcc_task_detail:
    description: "线索&工单管理-主任务列表-任务详情"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 主任务列表
      - action: click
        selector: 主任务列表筛选线索id输入框
      - action: fill
        selector: 主任务列表筛选线索id输入框
        value: "22520906127532"
      - action: press_key
        selector: 主任务列表筛选线索id输入框
        value: Enter
      - action: click
        selector: 主任务列表筛选主任务类型
      - action: fill
        selector: 主任务列表筛选主任务类型
        value: "待清洗"
      - action: press_key
        selector: 主任务列表筛选主任务类型
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action:  click
        selector: 主任务列表查看任务详情
      - action: wait
        value: 2
      - action: assert_text
        selector: 主任务列表任务详情信息1验证
        value: "22520906127532"
      - action: assert_text
        selector: 主任务列表任务详情信息2验证
        value: "22520906127788"
      - action: assert_text
        selector: 主任务列表任务详情信息3验证
        value: "待清洗"
      - action: assert_text
        selector: 主任务列表任务详情信息4验证
        value: "2024-09-21 16:00:39"
      - action: assert_text
        selector: 主任务列表任务详情信息5验证
        value: "2024-09-21 16:00:39"
      - action: assert_text
        selector: 主任务列表任务详情信息6验证
        value: "2024-09-21 22:00:00"
      - action: assert_text
        selector: 主任务列表任务详情信息7验证
        value: "2024-09-21 22:00:00"
      - action: assert_text
        selector: 主任务列表任务详情信息8验证
        value: "已完成"
      - action: assert_text
        selector: 主任务列表任务详情信息9验证
        value: "2024-09-21 16:02:13"
      - action: assert_text
        selector: 主任务列表任务详情信息10验证
        value: "香港-新零售加盟商测试租户-加盟商香港综合测试门店1"
      - action: assert_text
        selector: 主任务列表任务详情信息11验证
        value: "管家2"
      - action: assert_text
        selector: 主任务列表任务详情信息12验证
        value: "H:7天内买车"
      - action: assert_text
        selector: 主任务列表任务详情信息13验证
        value: "有效-预计到店-到门店-全息体验,现场活动,试乘试驾"
      - action: assert_text
        selector: 主任务列表任务详情信息14验证
        value: "2024-09-21"
      - action: assert_text
        selector: 主任务列表任务详情信息15验证
        value: "奔驰Arocs 2036 17:15:00"

  test_dcc_task_new_detail:
    description: "线索&工单管理-主任务列表-任务详情"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 主任务列表
      - action: click
        selector: 主任务列表筛选线索id输入框
      - action: fill
        selector: 主任务列表筛选线索id输入框
        value: "22520906127532"
      - action: press_key
        selector: 主任务列表筛选线索id输入框
        value: Enter
      - action: click
        selector: 主任务列表筛选主任务类型
      - action: fill
        selector: 主任务列表筛选主任务类型
        value: "待清洗"
      - action: press_key
        selector: 主任务列表筛选主任务类型
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: expect_popup
        rel_action: click
        selector: 主任务列表组合筛选验证
        variable: pop_index
      - action: wait
        value: 2
      - action: assert_text
        selector: 主任务列表新线索详情页信息1验证
        value: "22520906127532"
      - action: assert_text
        selector: 主任务列表新线索详情页信息2验证
        value: "任务信息"
      - action: assert_text
        selector: 主任务列表新线索详情页信息3验证
        value: "宝马-宝马5系-2024款 525Li M运动套装"