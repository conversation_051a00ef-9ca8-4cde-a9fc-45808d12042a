test_data:
  test_lead_mange_search_lead:
    description: "线索&工单管理-线索管理-线索id搜索"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选线索id
      - action: fill
        selector: 线索管理筛选线索id
        value: "26688865194598"
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理筛选线索id验证
        value: "26688865194598"



  test_lead_mange_search_phone:
    description: "线索&工单管理-线索管理-手机号搜索"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选手机号输入框
      - action: fill
        selector: 线索管理筛选手机号输入框
        value: "15233224950"
      - action: wait
        value: 2
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理筛选手机号搜索结果验证
        value: 152****4950

  test_lead_mange_detail:
    description: "线索&工单管理-线索管理-查看详情"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选线索id
      - action: fill
        selector: 线索管理筛选线索id
        value: "26688865194598"
      - action: click
        selector: 线索管理筛选查询按钮
      - action: expect_popup
        rel_action : click
        selector: 线索管理查看详情
        variable : pop_index
      - action: wait
        value: 5
      - action: assert_text
        selector: 线索管理详情页验证
        value: 用户基本信息
      - action: assert_text
        selector: 线索管理查看详情验证
        value: 182****3933
      - action: assert_text
        selector: 线索管理详情页验证1
        value: 所有跟进记录


  test_lead_mange_search_1:
    description: "线索&工单管理-线索管理-组合搜索1"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选创建日期开始
      - action: fill
        selector: 线索管理筛选创建日期开始
        value: "2025-03-24"
      - action: press_key
        selector: 线索管理筛选创建日期开始
        value: Enter
      - action: wait
        value: 1
      - action: click
        selector: 线索管理筛选创建日期结束
      - action: fill
        selector: 线索管理筛选创建日期结束
        value: "2025-03-24"
      - action: press_key
        selector: 线索管理筛选创建日期结束
        value: Enter
      - action: click
        selector: 线索管理筛选进店日期开始
      - action: fill
        selector: 线索管理筛选进店日期开始
        value: "2025-03-24"
      - action: press_key
        selector: 线索管理筛选进店日期开始
        value: Enter
      - action: wait
        value: 1
      - action: click
        selector: 线索管理筛选进店日期结束
      - action: fill
        selector: 线索管理筛选进店日期结束
        value: "2025-03-24"
      - action: press_key
        selector: 线索管理筛选进店日期结束
        value: Enter
      - action: click
        selector: 线索管理筛选一级来源
      - action: fill
        selector: 线索管理筛选一级来源
        value: "新零售-手工录入"
      - action: press_key
        selector: 线索管理筛选一级来源
        value: Enter
      - action: click
        selector: 线索管理筛选二级来源
      - action: fill
        selector: 线索管理筛选二级来源
        value: "自然进店"
      - action: press_key
        selector: 线索管理筛选二级来源
        value: Enter
      - action: click
        selector: 线索管理筛选三级来源
      - action: fill
        selector: 线索管理筛选三级来源
        value: "自然进店"
      - action: press_key
        selector: 线索管理筛选三级来源
        value: Enter
      - action: click
        selector: 线索管理筛选品牌
      - action: fill
        selector: 线索管理筛选品牌
        value: "金杯"
      - action: press_key
        selector: 线索管理筛选品牌
        value: Enter
      - action: click
        selector: 线索管理筛选购车周期
      - action: fill
        selector: 线索管理筛选购车周期
        value: "存疑"
      - action: press_key
        selector: 线索管理筛选购车周期
        value: Enter
      - action: click
        selector: 线索管理筛选跟进人
      - action: fill
        selector: 线索管理筛选跟进人
        value: "管家2"
      - action: press_key
        selector: 线索管理筛选跟进人
        value: Enter
      - action: click
        selector: 线索管理筛选状态
      - action: fill
        selector: 线索管理筛选状态
        value: "战败"
      - action: press_key
        selector: 线索管理筛选状态
        value: Enter
      - action: click
        selector: 线索管理筛选手机归属地
      - action: fill
        selector: 线索管理筛选手机归属地
        value: "北京"
      - action: press_key
        selector: 线索管理筛选手机归属地
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理筛选线索id验证
        value: "26688865194598"


  test_lead_mange_search_status:
    description: "线索&工单管理-线索管理-各状态搜索"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选状态
      - action: fill
        selector: 线索管理筛选状态
        value: "下订"
      - action: press_key
        selector: 线索管理筛选状态
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理列表状态下订验证
        value: "下订"
      - action: wait
        value: 1
      - action: refresh
      - action: click
        selector: 线索管理筛选状态
      - action: fill
        selector: 线索管理筛选状态
        value: "战败"
      - action: press_key
        selector: 线索管理筛选状态
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理列表状态战败验证
        value: "战败"
      - action: refresh
      - action: click
        selector: 线索管理筛选状态
      - action: fill
        selector: 线索管理筛选状态
        value: "待清洗"
      - action: press_key
        selector: 线索管理筛选状态
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理列表状态待清洗验证
        value: "待清洗"
      - action: refresh
      - action: click
        selector: 线索管理筛选状态
      - action: fill
        selector: 线索管理筛选状态
        value: "已接待"
      - action: press_key
        selector: 线索管理筛选状态
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理列表状态已接待验证
        value: "已接待"
      - action: refresh
      - action: click
        selector: 线索管理筛选状态
      - action: fill
        selector: 线索管理筛选状态
        value: "持续跟进"
      - action: press_key
        selector: 线索管理筛选状态
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理列表状态持续跟进验证
        value: "持续跟进"
      - action: refresh
      - action: click
        selector: 线索管理筛选状态
      - action: fill
        selector: 线索管理筛选状态
        value: "预约待接待"
      - action: press_key
        selector: 线索管理筛选状态
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理列表状态预约待接待验证
        value: "预约待接待"
      - action: refresh
      - action: click
        selector: 线索管理筛选状态
      - action: fill
        selector: 线索管理筛选状态
        value: "无效"
      - action: press_key
        selector: 线索管理筛选状态
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理列表状态无效验证
        value: "无效"
      - action: refresh
      - action: click
        selector: 线索管理筛选状态
      - action: fill
        selector: 线索管理筛选状态
        value: "重复"
      - action: press_key
        selector: 线索管理筛选状态
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理列表状态重复验证
        value: "重复"
      - action: refresh
      - action: click
        selector: 线索管理筛选状态
      - action: fill
        selector: 线索管理筛选状态
        value: "咨询待预约"
      - action: press_key
        selector: 线索管理筛选状态
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理列表状态咨询待预约验证
        value: "咨询待预约"


  test_lead_mange_search_source:
    description: "线索&工单管理-线索管理-来源搜索"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选一级来源
      - action: fill
        selector: 线索管理筛选一级来源
        value: "新零售-手工录入"
      - action: press_key
        selector: 线索管理筛选一级来源
        value: Enter
      - action: click
        selector: 线索管理筛选二级来源
      - action: fill
        selector: 线索管理筛选二级来源
        value: "自然进店"
      - action: press_key
        selector: 线索管理筛选二级来源
        value: Enter
      - action: click
        selector: 线索管理筛选三级来源
      - action: fill
        selector: 线索管理筛选三级来源
        value: "自然进店"
      - action: press_key
        selector: 线索管理筛选三级来源
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text
        selector: 线索管理列表来源新零售录入验证
        value: "新零售-手工录入-自然进店-自然进店 自然进店"
      - action: refresh
      - action: click
        selector: 线索管理筛选一级来源
      - action: fill
        selector: 线索管理筛选一级来源
        value: "新零售-系统导入"
      - action: press_key
        selector: 线索管理筛选一级来源
        value: Enter
      - action: click
        selector: 线索管理筛选二级来源
      - action: fill
        selector: 线索管理筛选二级来源
        value: "平安定向"
      - action: press_key
        selector: 线索管理筛选二级来源
        value: Enter
      - action: click
        selector: 线索管理筛选三级来源
      - action: fill
        selector: 线索管理筛选三级来源
        value: "平安理赔"
      - action: press_key
        selector: 线索管理筛选三级来源
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text_contains
        selector: 线索管理列表来源新零售导入验证
        value: "新零售-系统导入-平安定向-平安理赔"
      - action: refresh
      - action: click
        selector: 线索管理筛选一级来源
      - action: fill
        selector: 线索管理筛选一级来源
        value: "新零售-线上位置"
      - action: press_key
        selector: 线索管理筛选一级来源
        value: Enter
      - action: click
        selector: 线索管理筛选二级来源
      - action: fill
        selector: 线索管理筛选二级来源
        value: "抖音App"
      - action: press_key
        selector: 线索管理筛选二级来源
        value: Enter
      - action: click
        selector: 线索管理筛选三级来源
      - action: fill
        selector: 线索管理筛选三级来源
        value: "默认推广位"
      - action: press_key
        selector: 线索管理筛选三级来源
        value: Enter
      - action: click
        selector: 线索管理筛选查询按钮
      - action: assert_text_contains
        selector: 线索管理列表来源新零售线上位置验证
        value: "新零售-线上位置-抖音App-默认推广位"



  test_lead_mange_list:
    description: "线索&工单管理-线索管理-线索id搜索"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选线索id
      - action: fill
        selector: 线索管理筛选线索id
        value: "26688865194598"
      - action: click
        selector: 线索管理筛选查询按钮
      - action: wait
        value: 5
      - action: assert_text
        selector: 线索管理筛选线索id验证
        value: "26688865194598"
      - action: assert_text
        selector: 线索管理列表姓名验证
        value: "客户名称"
      - action: assert_text
        selector: 线索管理列表手机号验证
        value: "182****3933"
      - action: assert_text
        selector: 线索管理列表归属地验证
        value: "北京"
      - action: assert_text
        selector: 线索管理列表购车城市验证
        value: "香港"
      - action: assert_text
        selector: 线索管理列表购车周期验证
        value: "存疑"
      - action: assert_text
        selector: 线索管理列表购车范围验证
        value: "-"
      - action: assert_text
        selector: 线索管理列表意向车型验证
        value: "金杯-新海狮X30L CNG-2023款 70L CNG财富版客车6/7座无空调SWCC15M"
      - action: assert_text
        selector: 线索管理列表创建时间验证
        value: "2025-03-24 16:32:21"
      - action: assert_text
        selector: 线索管理列表进店时间验证
        value: "2025-03-24 16:32:22"
      - action: assert_text
        selector: 线索管理列表跟进人验证
        value: "管家2"


  test_lead_mange_Revert_assign:
    description: "线索&工单管理-线索管理-重新分配"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选线索id
      - action: fill
        selector: 线索管理筛选线索id
        value: "26551228053131"
      - action: click
        selector: 线索管理筛选查询按钮
      - action: click
        selector: 线索管理列表重新分配
      - action: wait
        value: 2
      - action: click
        selector: 线索管理列表重新分配输入框
      - action: fill
        selector: 线索管理列表重新分配输入框
        value: "管家2"
      - action: press_key
        selector: 线索管理列表重新分配输入框
        value: Enter
      - action: click
        selector: 线索管理列表分配弹窗确定按钮
      - action: assert_text
        selector: 分配成功验证
        value: "分配成功"

  test_lead_mange_batch_assign:
    description: "线索&工单管理-线索管理-批量分配"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选线索id
      - action: fill
        selector: 线索管理筛选线索id
        value: "26551228053131"
      - action: click
        selector: 线索管理筛选查询按钮
      - action: click
        selector: 线索管理列表全选
      - action: click
        selector: 线索管理列表批量分配
      - action: wait
        value: 2
      - action: fill
        selector: 线索管理列表批量分配输入框
        value: "1"
      - action: click
        selector: 线索管理列表分配弹窗确定按钮
      - action: assert_text
        selector: 分配成功验证
        value: "分配成功"

  test_lead_mange_new_detail1:
    description: "线索&工单管理-线索管理-线索id查看详情-待清洗任务验证"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选线索id
      - action: fill
        selector: 线索管理筛选线索id
        value: "26780204022329"
      - action: click
        selector: 线索管理筛选查询按钮
      - action: expect_popup
        rel_action : click
        selector: 线索管理列表线索id
        variable : pop_index
      - action: wait
        value: 2
      - action: assert_text
        selector: 新详情1线索id验证
        value: "26780204022329"
      - action: assert_text
        selector: 新详情1居住地址验证
        value: "-"
      - action: assert_text
        selector: 新详情1居住姓名验证
        value: "客户名称"
      - action: assert_text
        selector: 新详情1居住创建时间验证
        value: "2025-03-28 17:19:31"
      - action: assert_text
        selector: 新详情1居住手机号验证
        value: "182****3933"
      - action: assert_text
        selector: 新详情1居住来源验证
        value: "新零售-手工录入-自然进店-自然进店"
      - action: assert_text
        selector: 新详情1居住城市验证
        value: "香港"
      - action: assert_text
        selector: 新详情1居住购车周期验证
        value: "存疑"
      - action: assert_text
        selector: 新详情1居住意向车型验证
        value: "金杯-新海狮X30L CNG-2023款 70L CNG财富版客车6/7座无空调SWCC15M"
      - action: assert_text
        selector: 新详情1任务列表线索id验证
        value: "26780204022329"
      - action: assert_text
        selector: 新详情1任务列表主任务id验证
        value: "26780204022585"
      - action: assert_text
        selector: 新详情1任务列表任务类型验证
        value: "待清洗"
      - action: assert_text
        selector: 新详情1任务列表逾期时间验证
        value: "2025-03-28 22:00:00"
      - action: assert_text
        selector: 新详情1任务列表创建时间验证
        value: "2025-03-28 17:19:31"
      - action: assert_text
        selector: 新详情1任务列表完成时间验证
        value: "2025-03-28 17:19:39"
      - action: assert_text
        selector: 新详情1任务列表计划联系时间验证
        value: "2025-03-28 22:00:00"
      - action: assert_text
        selector: 新详情1任务列表状态验证
        value: "已完成"
      - action: assert_text
        selector: 新详情1任务列表门店验证
        value: "加盟商香港综合测试门店1"
      - action: assert_text
        selector: 新详情1任务列表员工验证
        value: "管家2"
      - action: assert_text
        selector: 新详情1任务列表跟进人验证
        value: "管家2"


  test_lead_mange_new_detail2:
    description: "线索&工单管理-线索管理-线索id查看详情-待回电任务验证"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选线索id
      - action: fill
        selector: 线索管理筛选线索id
        value: "26421067515981"
      - action: click
        selector: 线索管理筛选查询按钮
      - action: expect_popup
        rel_action : click
        selector: 线索管理列表线索id
        variable : pop_index
      - action: wait
        value: 2
      - action: assert_text
        selector: 新详情2任务列表线索id验证
        value: "26421067515981"
      - action: assert_text
        selector: 新详情2任务列表主任务id验证
        value: "26421069103325"
      - action: assert_text
        selector: 新详情2任务列表任务类型验证
        value: "待回电"
      - action: assert_text
        selector: 新详情2任务列表用户诉求验证
        value: "买车议价"
      - action: assert_text
        selector: 新详情2任务列表逾期时间验证
        value: "2025-03-12 22:00:00"
      - action: assert_text
        selector: 新详情2任务列表创建时间验证
        value: "2025-03-12 20:46:20"
      - action: assert_text
        selector: 新详情2任务列表完成时间验证
        value: "2025-03-12 20:46:22"
      - action: assert_text
        selector: 新详情2任务列表计划联系时间验证
        value: "2025-03-12 21:00:00"
      - action: assert_text
        selector: 新详情2任务列表状态验证
        value: "已完成"
      - action: assert_text
        selector: 新详情2任务列表门店验证
        value: "加盟商香港综合测试门店1"
      - action: assert_text
        selector: 新详情2任务列表员工验证
        value: "管家2"
      - action: assert_text
        selector: 新详情2任务列表跟进人验证
        value: "管家2"


  test_lead_mange_new_detail3:
    description: "线索&工单管理-线索管理-线索id查看详情-持续跟进任务验证"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选线索id
      - action: fill
        selector: 线索管理筛选线索id
        value: "26780204022329"
      - action: click
        selector: 线索管理筛选查询按钮
      - action: expect_popup
        rel_action : click
        selector: 线索管理列表线索id
        variable : pop_index
      - action: wait
        value: 2
      - action: assert_text
        selector: 新详情3任务列表线索id验证
        value: "26780204022329"
      - action: assert_text
        selector: 新详情3任务列表主任务id验证
        value: "26780209257082"
      - action: assert_text
        selector: 新详情3任务列表任务类型验证
        value: "持续跟进"
      - action: assert_text
        selector: 新详情3任务列表再跟进原因验证
        value: "-"
      - action: assert_text
        selector: 新详情3任务列表逾期时间验证
        value: "2025-03-28 22:00:00"
      - action: assert_text
        selector: 新详情3任务列表创建时间验证
        value: "2025-03-28 17:19:51"
      - action: assert_text
        selector: 新详情3任务列表完成时间验证
        value: "2025-03-28 17:19:56"
      - action: assert_text
        selector: 新详情3任务列表计划联系时间验证
        value: "2025-03-28 22:00:00"
      - action: assert_text
        selector: 新详情3任务列表状态验证
        value: "已完成"
      - action: assert_text
        selector: 新详情3任务列表门店验证
        value: "系统"
      - action: assert_text
        selector: 新详情3任务列表员工验证
        value: "系统"
      - action: assert_text
        selector: 新详情3任务列表跟进人验证
        value: "管家2"

  test_lead_mange_new_detail4:
    description: "线索&工单管理-线索管理-线索id查看详情-待接待任务验证"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选线索id
      - action: fill
        selector: 线索管理筛选线索id
        value: "26780204022329"
      - action: click
        selector: 线索管理筛选查询按钮
      - action: expect_popup
        rel_action : click
        selector: 线索管理列表线索id
        variable : pop_index
      - action: wait
        value: 2
      - action: assert_text
        selector: 新详情4任务列表线索id验证
        value: "26780204022329"
      - action: assert_text
        selector: 新详情4任务列表主任务id验证
        value: "26780206115704"
      - action: assert_text
        selector: 新详情4任务列表任务到店类型验证
        value: "到门店"
      - action: assert_text
        selector: 新详情4任务列表任务类型验证
        value: "待接待"
      - action: assert_text
        selector: 新详情4任务列表到店诉求验证
        value: "现场活动"
      - action: assert_text
        selector: 新详情4任务列表逾期时间验证
        value: "2025-03-28 22:00:00"
      - action: assert_text
        selector: 新详情4任务列表创建时间验证
        value: "2025-03-28 17:19:39"
      - action: assert_text
        selector: 新详情4任务列表完成时间验证
        value: "2025-03-28 17:19:47"
      - action: assert_text
        selector: 新详情4任务列表预约到店时间验证
        value: "2025-03-28 17:30:00"
      - action: assert_text
        selector: 新详情4任务列表状态验证
        value: "已完成"
      - action: assert_text
        selector: 新详情4任务列表门店验证
        value: "加盟商香港综合测试门店1"
      - action: assert_text
        selector: 新详情4任务列表员工验证
        value: "管家2"
      - action: assert_text
        selector: 新详情4任务列表跟进人验证
        value: "管家2"

  test_lead_mange_new_detail_follow:
    description: "线索&工单管理-线索管理-线索id查看详情-对客作业列表信息验证"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选线索id
      - action: fill
        selector: 线索管理筛选线索id
        value: "26780204022329"
      - action: click
        selector: 线索管理筛选查询按钮
      - action: expect_popup
        rel_action : click
        selector: 线索管理列表线索id
        variable : pop_index
      - action: wait
        value: 2
      - action: click
        selector: 新详情对客作业按钮
      - action: assert_text
        selector: 对客作业信息1验证
        value: "任务跟进"
      - action: assert_text
        selector: 对客作业信息2验证
        value: "持续跟进"
      - action: assert_text
        selector: 对客作业信息3验证
        value: "战败"
      - action: assert_text
        selector: 对客作业信息4验证
        value: "管家2"
      - action: assert_text
        selector: 对客作业信息5验证
        value: "任务跟进"
      - action: assert_text
        selector: 对客作业信息6验证
        value: "待接待"
      - action: assert_text
        selector: 对客作业信息7验证
        value: "待接待"
      - action: assert_text
        selector: 对客作业信息8验证
        value: "管家2"
      - action: assert_text
        selector: 对客作业信息9验证
        value: "线索推送"
      - action: assert_text
        selector: 对客作业信息10验证
        value: "管家2"
      - action: assert_text
        selector: 对客作业信息11验证
        value: "金杯-新海狮X30L CNG-2023款 70L CNG财富版客车6/7座无空调SWCC15M"
      - action: assert_text
        selector: 对客作业信息12验证
        value: "随机经销商"
      - action: assert_text
        selector: 对客作业信息13验证
        value: "任务跟进"
      - action: assert_text
        selector: 对客作业信息14验证
        value: "待清洗"
      - action: assert_text
        selector: 对客作业信息15验证
        value: "有效-预计到店-到门店-现场活动"
      - action: assert_text
        selector: 对客作业信息16验证
        value: "管家2"
      - action: assert_text
        selector: 对客作业信息17验证
        value: "26780204022329"
      - action: assert_text
        selector: 对客作业信息18验证
        value: "客户名称"
      - action: assert_text
        selector: 对客作业信息19验证
        value: "创建线索"
      - action: assert_text
        selector: 对客作业信息20验证
        value: "26780204022329"
      - action: assert_text
        selector: 对客作业信息21验证
        value: "客户名称"


  test_lead_mange_new_detail_follow_detail:
    description: "线索&工单管理-线索管理-线索id查看详情-对客作业列表-任务详情信息验证"
    steps:
      - use_module: login_steps
      - action: click
        selector: 线索&工单管理
      - action: click
        selector: 线索管理
      - action: click
        selector: 线索管理筛选线索id
      - action: fill
        selector: 线索管理筛选线索id
        value: "26780204022329"
      - action: click
        selector: 线索管理筛选查询按钮
      - action: expect_popup
        rel_action : click
        selector: 线索管理列表线索id
        variable : pop_index
      - action: wait
        value: 2
      - action: click
        selector: 新详情对客作业按钮
      - action: click
        selector: 对客作业详情信息按钮
      - action: assert_text
        selector: 对客作业详情信息1验证
        value: "26780204022329"
      - action: assert_text
        selector: 对客作业详情信息2验证
        value: "26780204022585"
      - action: assert_text
        selector: 对客作业详情信息3验证
        value: "待清洗"
      - action: assert_text
        selector: 对客作业详情信息4验证
        value: "2025-03-28 17:19:31"
      - action: assert_text
        selector: 对客作业详情信息5验证
        value: "2025-03-28 17:19:32"
      - action: assert_text
        selector: 对客作业详情信息6验证
        value: "2025-03-28 22:00:00"
      - action: assert_text
        selector: 对客作业详情信息7验证
        value: "2025-03-28 22:00:00"
      - action: assert_text
        selector: 对客作业详情信息8验证
        value: "2025-03-28 17:19:39"
      - action: assert_text
        selector: 对客作业详情信息9验证
        value: "香港-新零售加盟商测试租户-加盟商香港综合测试门店1"
      - action: assert_text
        selector: 对客作业详情信息10验证
        value: "管家2"
      - action: assert_text
        selector: 对客作业详情信息11验证
        value: "存疑"
      - action: assert_text
        selector: 对客作业详情信息12验证
        value: "有效-预计到店-到门店-现场活动"
      - action: assert_text
        selector: 对客作业详情信息13验证
        value: "2025-03-28"
