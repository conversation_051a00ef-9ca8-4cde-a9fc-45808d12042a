filter_steps: # 按城市和车型筛选政策
  - action: click
    selector: 经销商省份筛选框
  - action: fill
    selector: 经销商省份筛选框
    value: "香港"
  - action: click
    selector: 选择经销商省份
  - action: click
    selector: 经销商城市筛选框
  - action: fill
    selector: 经销商城市筛选框
    value: "香港"
  - action: click
    selector: 选择经销商城市
  - action: click
    selector: 经销商区域筛选框
  - action: click
    selector: 选择经销商区域
  - action: click
    selector: 品牌筛选框
  - action: fill
    selector: 品牌筛选框
    value: "奥迪"
  - action: click
    selector: 选择品牌
  - action: click
    selector: 车系筛选框
  - action: fill
    selector: 车系筛选框
    value: "奥迪A4"
  - action: click
    selector: 选择车系
  - action: click
    selector: 车型筛选框
  - action: click
    selector: 选择车型
  - action: click
    selector: 查询按钮

list_add_data_steps: #列表新建数据验证
  - action: assert_text
    selector: 新建列表数据奥迪
    value: 奥迪
  - action: assert_text
    selector: 新建列表数据奥迪A4L
    value: 奥迪A4L
  - action: assert_text
    selector: 新建列表数据车型
    value: 2025款 200万辆悦享版 40 TFSI 时尚动感型
  - action: assert_text
    selector: 新建列表数据香港
    value: 香港
  - action: assert_text
    selector: 新建列表数据测试宝骏
    value: 测试宝骏20150609
  - action: assert_text
    selector: 新建列表数据厂商指导价
    value: "28.980"
  - action: assert_text
    selector: 新建列表数据库存状态列
    value: 现车
  - action: assert_text
    selector: 新建列表数据交货期
    value: 1周
  - action: assert_text
    selector: 新建列表数据裸车价
    value: "28.780"
  - action: assert_text
    selector: 新建列表数据DCC线上报价
    value: "28.780"
  - action: assert_text
    selector: 新建列表数据金融手续费列
    value: "1.000"
