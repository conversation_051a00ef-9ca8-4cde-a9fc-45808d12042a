elements:


  线索&工单管理: role=complementary >> text=线索&工单管理
  主任务列表: text=主任务列表
  主任务列表筛选手机号输入框: //input[@id='basic_userPhone']
  主任务列表筛选线索id输入框: //input[@id='basic_leadId']
  主任务列表筛选查询按钮: text="查 询"
  主任务列表查看任务详情: (//a[contains(text(),'任务详情')])[1]
  主任务列表筛选任务创建日期开始 : (//input[@id='basic_createdDate'])[1]
  主任务列表筛选任务创建日期结束 : (//input[@placeholder='结束时间'])[1]
  主任务列表筛选分配日期开始: (//input[@id='basic_allotTimeDate'])[1]
  主任务列表筛选分配日期结束: (//input[@placeholder='结束时间'])[2]
  主任务列表筛选计划联系日期开始: (//input[@id='basic_planConnectTime'])[1]
  主任务列表筛选计划联系日期结束: (//input[@placeholder='结束时间'])[3]
  主任务列表筛选逾期日期开始: (//input[@id='basic_overDueTaskDate'])[1]
  主任务列表筛选逾期日期结束: (//input[@placeholder='结束时间'])[4]
  主任务列表筛选实际完成日期开始: (//input[@id='basic_actualFinishTime'])[1]
  主任务列表筛选实际完成日期结束: (//input[@placeholder='结束时间'])[5]
  主任务列表筛选创建集团: (//input[@id='basic_creatorTenantId'])[1]
  主任务列表筛选跟进人: (//input[@id='basic_workerId'])[1]
  主任务列表筛选主任务状态: (//input[@id='basic_status'])[1]
  主任务列表筛选主任务类型: (//input[@id='basic_taskType'])[1]
  主任务列表组合筛选验证: (//a[normalize-space()='22520906127532'])[1]
  主任务列表列表姓名验证 : (//span[normalize-space()='ces'])[1]
  主任务列表列表手机号验证 : (//span[normalize-space()='152****4950'])[1]
  主任务列表列表线索状态验证: (//span[contains(text(),'完成')])[1]
  主任务列表列表来源验证 : (//span[contains(text(),'新零售-手工录入-自然进店-自然进店')])[1]
  主任务列表列表主任务id验证 : (//span[normalize-space()='22520906127788'])[1]
  主任务列表列表任务类型验证 : (//span[contains(text(),'待清洗')])[1]
  主任务列表列表任务类型待回电验证 : (//span[contains(text(),'待回电')])[1]
  主任务列表列表任务类型持续跟进验证 : (//span[contains(text(),'持续跟进')])[1]
  主任务列表列表任务类型待接待验证 : (//span[contains(text(),'待接待')])[1]
  主任务列表列表主任务状态验证 : (//span[@class='ant-tag ant-tag-default mainTask-status-tag'])[1]
  主任务列表列表创建时间验证 : (//span[contains(text(),'2024-09-21 16:00:39')])[1]
  主任务列表列表分配时间验证 : (//span[contains(text(),'2024-09-21 16:00:39')])[2]
  主任务列表列表计划时间验证 : (//span[contains(text(),'2024-09-21 22:00:00')])[1]
  主任务列表列表逾期时间验证 : (//span[contains(text(),'2024-09-21 22:00:00')])[2]
  主任务列表列表实际完成时间验证 : (//span[normalize-space()='2024-09-21 16:02:13'])[1]
  主任务列表列表集团验证 : (//span[contains(text(),'新零售加盟商测试租户')])[2]
  主任务列表列表门店验证 : (//span[contains(text(),'加盟商香港综合测试门店1')])[1]
  主任务列表列表员工验证 : (//span[contains(text(),'管家2')])[1]
  主任务列表列表跟进人验证 : (//td[contains(text(),'管家2')])[1]
  主任务列表列表主任务状态服务中验证 : (//div)[175]
  主任务列表任务详情信息1验证: (//span[normalize-space()='22520906127532'])[1]
  主任务列表任务详情信息2验证: (//span[@class='record-call-value'][normalize-space()='22520906127788'])[1]
  主任务列表任务详情信息3验证: (//span[@class='record-call-value'][contains(text(),'待清洗')])[1]
  主任务列表任务详情信息4验证: (//span[contains(text(),'2024-09-21 16:00:39')])[3]
  主任务列表任务详情信息5验证: (//span[contains(text(),'2024-09-21 16:00:39')])[4]
  主任务列表任务详情信息6验证: (//span[contains(text(),'2024-09-21 22:00:00')])[3]
  主任务列表任务详情信息7验证: (//span[contains(text(),'2024-09-21 22:00:00')])[3]
  主任务列表任务详情信息8验证: (//span[contains(text(),'已完成')])[1]
  主任务列表任务详情信息9验证: (//span[@class='record-call-value'][normalize-space()='2024-09-21 16:02:13'])[1]
  主任务列表任务详情信息10验证: (//span[contains(text(),'香港-新零售加盟商测试租户-加盟商香港综合测试门店1')])[1]
  主任务列表任务详情信息11验证: (//span[@class='record-call-value'][contains(text(),'管家2')])[1]
  主任务列表任务详情信息12验证: (//span[contains(text(),'H:7天内买车')])[1]
  主任务列表任务详情信息13验证: (//span[contains(text(),'有效-预计到店-到门店-全息体验,现场活动,试乘试驾')])[1]
  主任务列表任务详情信息14验证: (//span[normalize-space()='2024-09-21'])[1]
  主任务列表任务详情信息15验证: (//span[contains(text(),'奔驰Arocs 2036 17:15:00')])[1]
  主任务列表新线索详情页信息1验证 : (//div[normalize-space()='22520906127532'])[1]
  主任务列表新线索详情页信息2验证 : (//th[contains(text(),'任务信息')])[1]
  主任务列表新线索详情页信息3验证 : (//div[contains(text(),'宝马-宝马5系-2024款 525Li M运动套装')])[1]