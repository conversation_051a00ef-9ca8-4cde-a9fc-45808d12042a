elements:

  车系配置: //span[@class='ant-menu-title-content']//div
  新建: (//button[@type='button'])[3]
  点击查看/修改: //div[@class='ant-col ant-col-16 ant-form-item-control']//button[@type='button']
  展开: (//span[@class='ant-tree-switcher ant-tree-switcher_close'])[1]
  奥迪A8: //div[4]//span[3]//span[1]
  右移: span[aria-label='right']
  确定: //span[contains(text(),'确 定')]
  品牌类别C: //input[@value='C']
  直推车否: //input[@value='0']
  序号: //input[@placeholder='1-99999']
  提交: //span[contains(text(),'提 交')]

  筛选车系: //input[@id='basic_seriesName']
  查询: //span[contains(text(),'查 询')]
  修改: //span[contains(text(),'修改')]
  已选中: (//span[@class='ant-tree-checkbox-inner'])[460]
  左移: //span[@aria-label='left']
  奥迪A6: //div[8]//span[3]//span[1]

  位置下拉框: //input[@id='basic_position']
  频道页车源列表位置: //div[@id='365']//div[1]
  车系输入框: //input[@id='basic_seriesName']
  品牌: //input[@id='basic_brandId']
  品牌筛选: //div[@title='奥迪']//div[1]
  主推筛选: //input[@id='basic_isMainRecommend']
  是主推: //div[@title='是']//div[1]
  品牌类别: //input[@id='basic_brandType']
  品牌类别筛选C: //div[@class='ant-select-item-option-content'][normalize-space()='C']
  启用状态: //input[@id='basic_status']
  启用: //div[@title='启用']//div[1]
  筛选查询: //span[contains(text(),'查 询')]
  列表位置: (//td[contains(text(),'频道页车源列表')])[1]
  列表品牌: (//td[@class='ant-table-cell'][contains(text(),'奥迪')])[1]
  列表车系: (//td[contains(text(),'奥迪A6L')])[1]
  列表类别: //td[normalize-space()='C']
  列表主推: //td[contains(text(),'是')]

  车系重置: //span[contains(text(),'重 置')]

  购车政策: //span[@title='购车政策']
  优惠权益配置: //li[@title='优惠权益配置']//div
  新建活动: //span[contains(text(),'新建活动')]
  位置选择: //div[@class='ant-select-selection-overflow']
  频道页双列车卡: (//div[@class='ant-select-item-option-content'])[1]
  权益类型: //input[@id='form_item_text.rightType']
  到店礼: (//div[@class='ant-select-item-option-content'])[8]
  价值: //input[@placeholder='请输入金额']
  标题: //input[@id='form_item_text.title']
  开始时间: //input[@id='form_item_time']
  年份选择: //div[@class='ant-picker-date-panel']//div[@class='ant-picker-header-view']
  初始时间: //button[normalize-space()='2020-2029']
  30年代: //div[normalize-space()='2030-2039']
  2030年: //div[normalize-space()='2030']
  月份选择: //td[@title='2030-01']
  2030-01-01: //td[@title='2030-01-01']
  起止时间确定: //li[@class='ant-picker-ok']
  结束时间: //input[@placeholder='结束时间']
  2030-01-06: //td[@title='2030-01-06']
  权益配置确定: (//span[contains(text(),'确 定')])[1]

  权益位置下拉框: //input[@id='form_item_position']
  频道页双列车卡选项: //div[@id='363']//div[1]
  权益类型下拉框: //input[@id='rc_select_1']
  到店礼选项: //div[@label='到店礼']
  权益标题: //input[@placeholder='请输入权益标题']
  权益查询: //span[contains(text(),'查 询')]
  查询结果标题: //td[contains(text(),'自动化测试001')]

  权益重置: //span[contains(text(),'重 置')]

  删除: //span[contains(text(),'删除')]
  取消: //div[@class='ant-modal-confirm-body-wrapper']//button[1]
  确认: //button[@class='ant-btn ant-btn-dangerous']
  暂无数据: //p[@class='ant-empty-description']






























