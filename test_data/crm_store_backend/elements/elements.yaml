elements:
  账号密码登录tab: text=账号密码登录
  账号输入框: //input[@id='phone']
  密码输入框: //input[@id='password']
  登录按钮: role=button[name="登 录"]

  购车政策: text="购车政策"
  购车政策列表: text="购车政策列表"

  新增按钮: role=button [name="+新增"]
  品牌下拉框: //input[@id='brandInfo']
  奥迪品牌: .ant-select-item-option-content
  车系列表: role=combobox [name="* 车系 :"]
  奥迪A4L车系: div[aria-selected="false"][title="奥迪A4L"]
  车型列表: role=combobox [name="* 车型 :"]
  款200万辆悦享版40TFSI时尚动感型: div[aria-selected="false"][title="289800"]
  经销商省份: role=combobox [name="* 经销商省份 :"]
  香港省份: div[aria-selected="false"][title="香港"]
  经销商城市: role=combobox [name="* 经销商城市 :"]
  香港城市: div[aria-selected="false"][title="香港"]
  经销商区域: role=combobox [name="* 经销商区域 :"]
  香港区域: div[aria-selected="false"][title="香港"]
  经销商名称: role=combobox [name="* 经销商名称 :"]
  测试宝骏经销商: div[aria-selected="false"][title="测试宝骏20150609"]
  库存状态: (//input[@id='stockTag'])[2]
  现车标签: div[aria-selected="false"][title="现车"]
  交货日期: (//input[@id='pickCycle'])[1]
  一周交货: div[aria-selected="false"][title="1周"]
  裸车价输入框: role=textbox [name="* 裸车价 :"]
  DCC报价输入框: role=textbox [name="* DCC报价/线上报价 :"]
  强制保险否: //*[@id="forceInsure"]/label[2]
  强制上牌否: //*[@id="forcePlate"]/label[2]
  本品本店置换: role=spinbutton [name="本品本店置换 :"]
  形式: (//input[@id='replacementType'])[1]
  抵扣车款: div[aria-selected="false"][title="抵扣车款"]
  添加按钮: role=button [name="添 加"]
  比例模式: text="比例模式"
  首付比例: role=spinbutton [name="首付比例"]
  免息期数: (//div[contains(@class,'ant-select-selector')])[22]
  12期: div[aria-selected="false"][title="12期"]
  低首付金融: role=spinbutton [name="* 低首付金融：首付低至 :"]
  金融手续费: role=textbox [name="* 金融手续费 :"]
  金融购车推荐方案: (//textarea[contains(@class,'ant-input')])[1]
  无绑定条件: //*[@id="bindCondition"]/label[1]
  无特殊颜色: //*[@id="specialColor"]/label[1]
  提交按钮: //span[contains(text(),'提 交')]

  新建列表数据奥迪: (//div[contains(text(),'奥迪')])[1]
  新建列表数据奥迪A4L: (//div[contains(text(),'奥迪A4L')])[1]
  新建列表数据车型: (//div[contains(text(),'2025款 200万辆悦享版 40 TFSI 时尚动感型')])[1]
  新建列表数据香港: //td[contains(text(),'香港')]
  新建列表数据测试宝骏: //td[contains(text(),'测试宝骏20150609')]
  新建列表数据厂商指导价: (//td[normalize-space()='28.980'])[1]
  新建列表数据库存状态列: (//td[@class='ant-table-cell'][contains(text(),'现车')])[1]
  新建列表数据交货期: (//td[@class='ant-table-cell'][contains(text(),'1周')])[1]
  新建列表数据裸车价: (//td[@class='ant-table-cell'][normalize-space()='28.780'])[1]
  新建列表数据DCC线上报价: (//td[@class='ant-table-cell'][normalize-space()='28.780'])[2]
  新建列表数据金融手续费列: (//td[@class='ant-table-cell'][normalize-space()='1.000'])[1]

  停用按钮: (//span[contains(text(),'停用')])[1]
  确认按钮: role=button[name="是"]
  启用状态筛选项: role=combobox[name="启用状态 :"]
  停用选项: text=停用

  查询按钮: //span[contains(text(),'查 询')]
  第一行: (//div[contains(text(),'2025款 200万辆悦享版 40 TFSI 时尚动感型')])[1]
  状态: (//span[contains(text(),'启用')])[1]

  编辑按钮: (//span[contains(text(),'编辑')])[1]
  #x详情元素
  新建详情品牌: (//div[@class="ant-col ant-col-16 ant-form-item-control"])[1]
  新建详情车系: (//div[@class="ant-col ant-col-16 ant-form-item-control"])[2]
  新建详情车型: (//div[@class="ant-col ant-col-16 ant-form-item-control"])[3]
  新建详情经销商省份: (//span[@title='香港'][contains(text(),'香港')])[1]
  新建详情经销商城市: (//span[@title='香港'][contains(text(),'香港')])[2]
  新建详情经销商区域: (//span[@title='香港'][contains(text(),'香港')])[3]
  新建详情经销商名称: span[title='测试宝骏20150609']
  新建详情库存状态: span[title='现车']
  新建详情交货日期: span[title='1周']
  新建详情裸车价: (//input[@id='nakedPrice'])[1]
  新建详情DCC报价: //input[@id='salesPrice']
  新建详情强制保险: //div[5]//div[3]
  新建详情强制上牌: (//input[@value='0'])[2]
  新建详情本品本店置换: //input[@id='sameProductSameStoreReplacement']
  新建详情形式: span[title='抵扣车款']
  新建详情比例模式: span[title='比例模式']
  新建详情首付比例: input[placeholder='首付比例']
  新建详情免息期数: span[title='12期']
  新建详情低首付金融: (//input[@id='downPayment'])[1]
  新建详情金融手续费: //input[@id='financialPoundage']
  新建详情金融购车推荐方案: textarea[class='ant-input']
  新建详情无绑定条件: (//span[@class='ant-radio ant-radio-checked'])[3]
  新建详情无特殊颜色: (//input[@value='0'])[4]
  新建详情提交按钮: //span[contains(text(),'提 交')]
  新建详情取消按钮: //span[contains(text(),'取 消')]

  删除按钮: (//span[contains(text(),'删除')])[1]
  确认删除: //span[contains(text(),'是')]

  经销商省份筛选框: //input[@id='provinceId']
  选择经销商省份: //div[contains(@title,'香港')]//div[1]
  经销商城市筛选框: //input[@id='cityId']
  选择经销商城市: (//div[contains(text(),'香港')])[2]
  经销商区域筛选框: //input[@id='countyId']
  选择经销商区域: (//div[contains(@class,'ant-select-item-option-content')][contains(text(),'香港')])[3]
  品牌筛选框: //input[@id='brandId']
  选择品牌: div[title='奥迪'] div[class='ant-select-item-option-content']
  车系筛选框: //input[@id='seriesId']
  选择车系: //div[contains(@title,'奥迪A4L')]//div[1]
  车型筛选框: //input[@id='specId']
  选择车型: //div[contains(@title,'2025款 200万辆悦享版 40 TFSI 时尚动感型')]//div[1]
  政策暂无数据: (//div[@class='ant-table-expanded-row-fixed'])[1]
  #编辑元素
  编辑库存状态输入框: span[title='现车']
  预定标签: //div[contains(@title,'预订')]//div[1]
  编辑交货日期输入框: (//div[contains(@class,'ant-col ant-col-16 ant-form-item-control')])[10]
  3月以上交货日期: //div[contains(@title,'3个月以上')]//div[1]
  强制保险是: (//input[contains(@value,'1')])[1]
  强制上牌是: (//input[contains(@value,'1')])[2]
  上牌费输入框: //input[@id='registerAmount']
  编辑形式输入框: span[title='抵扣车款']
  单独兑现: //div[contains(@title,'单独兑现')]//div[1]
  删除金融方案: //span[contains(text(),'删 除')]
  有绑定条件: (//span[contains(text(),'有')])[1]
  有特殊颜色: (//input[@value='1'])[4]
  颜色选择框: //div[@_nk='OCIdad']
  阿科纳白: div[title='阿科纳白'] div[class='ant-select-item-option-content']
  颜色价格输入框: (//input[contains(@autocomplete,'on')])[1]
  编辑列表数据库存状态列: //td[contains(text(),'预订')]

  #重置元素
  重置按钮: //span[contains(text(),'重 置')]
  重置后经销商省份筛选框为默认值: //span[contains(text(),'请选择经销商省份')]
  列表第二行数据序号: //td[normalize-space()='2']

 #编辑后的列表元素

  cell_1: tr[class='ant-table-row ant-table-row-level-0'] td:nth-child(1)
  text_audi: //td[@class='ant-table-cell ant-table-cell-fix-left']//div[1]
  text_a4l: div[_nk='aPX812']
  text_version: div[_nk='aPX813']
  cell_hk: tr[class='ant-table-row ant-table-row-level-0'] td:nth-child(1)
  cell_test: (//td[contains(text(),'测试宝骏20150609')])[1]
  cell_28_980: (//td[normalize-space()='28.980'])[1]
  cell_booking: (//td[contains(text(),'预订')])[1]
  cell_month: (//td[contains(text(),'3个月以上')])[1]
  裸车价cell_28_880: (//td[@class='ant-table-cell ant-table-cell-row-hover'][normalize-space()='28.879'])[1]
  线上报价cell_28_880: (//td[@class='ant-table-cell'][normalize-space()='28.880'])[1]
  text_white: span[_nk='aPX821']
  text_1_000: span[_nk='aPX822']
  强制保险cell_force: (//td[@class='ant-table-cell'][contains(text(),'强制')])[1]
  强制上牌cell_force: (//td[@class='ant-table-cell'][contains(text(),'强制')])[2]
  cell_0_400: (//td[normalize-space()='0.400'])[1]
  cell_0_900: (//td[normalize-space()='0.900'])[1]