elements:

  线索管理:  text=线索管理
  线索管理筛选手机号输入框: //input[@id='basic_userPhone']
  线索管理筛选查询按钮: text="查 询"
  线索管理筛选线索id验证 : //a[normalize-space()='26688865194598']
  线索管理筛选手机号搜索结果验证 : (//span[contains(text(),'152****4950')])[1]
  线索管理查看详情: (//a[@href='javascript:;'][contains(text(),'查看详情')])[1]
  线索管理查看详情验证: //li[normalize-space()='182****3933']
  线索管理详情页验证: //div[contains(text(),'用户基本信息')]
  线索管理详情页验证1: //div[contains(text(),'所有跟进记录')]
  线索管理筛选线索id : (//input[@id='basic_leadId'])[1]
  线索管理筛选创建日期开始 : //input[@id='basic_leadCreatedTime']
  线索管理筛选创建日期结束 : (//input[@placeholder='结束时间'])[1]
  线索管理筛选进店日期开始 : //input[@id='basic_allotStoreTime']
  线索管理筛选进店日期结束 : (//input[@placeholder='结束时间'])[2]
  线索管理筛选分配日期开始: //input[@id='basic_allotTime']
  线索管理筛选分配日期结束: (//input[@placeholder='结束时间'])[3]
  线索管理筛选一级来源: //input[@id='basic_pvSiteId']
  线索管理筛选二级来源: //input[@id='basic_pvCategoryId']
  线索管理筛选三级来源: //input[@id='basic_pvSubCategoryId']
  线索管理筛选品牌: //input[@id='basic_brandId']
  线索管理筛选车系: //input[@id='basic_seriesId']
  线索管理筛选购车周期: //input[@id='basic_buycarPeriodCode']
  线索管理筛选购车范围: //input[@id='basic_buycarTypeCode']
  线索管理筛选跟进人: //input[@id='basic_workerId']
  线索管理筛选状态: //input[@id='basic_leadStatusWithFinishReason']
  线索管理筛选手机归属地: //input[@id='basic_userPhoneCityId']
  线索管理列表状态下订验证: //td[contains(text(),'下订')]
  线索管理列表状态战败验证: //td[contains(text(),'战败')]
  线索管理列表状态待清洗验证 : //td[contains(text(),'待清洗')]
  线索管理列表状态已接待验证 : //td[contains(text(),'已接待')]
  线索管理列表状态持续跟进验证 : //td[contains(text(),'持续跟进')]
  线索管理列表状态预约待接待验证 : //td[contains(text(),'预约待接待')]
  线索管理列表状态无效验证 : //td[contains(text(),'无效')]
  线索管理列表状态重复验证 : //td[contains(text(),'重复')]
  线索管理列表状态咨询待预约验证 : //td[contains(text(),'咨询待预约')]
  线索管理列表来源新零售录入验证 : (//td[contains(text(),'新零售-手工录入-自然进店-自然进店 自然进店')])[1]
  线索管理列表来源新零售导入验证 : (//td[contains(text(),'新零售-系统导入-平安定向-平安理赔 平安理赔-重庆')])[1]
  线索管理列表来源新零售线上位置验证 : (//td[contains(text(),'新零售-线上位置-抖音App-默认推广位 抖音投放场景3-品牌')])[1]
  线索管理列表姓名验证 : //span[contains(text(),'客户名称')]
  线索管理列表手机号验证 : (//span[normalize-space()='182****3933'])[1]
  线索管理列表归属地验证 : (//span[contains(text(),'北京')])[1]
  线索管理列表购车城市验证 : (//span[contains(text(),'香港')])[1]
  线索管理列表购车周期验证 : (//span[contains(text(),'存疑')])[1]
  线索管理列表购车范围验证 : (//span[@class='zj-column-value'][normalize-space()='-'])[1]
  线索管理列表意向车型验证 : (//p[@class='series-item'])[1]
  线索管理列表创建时间验证 : (//td[normalize-space()='2025-03-24 16:32:21'])[1]
  线索管理列表进店时间验证 : (//td[normalize-space()='2025-03-24 16:32:22'])[1]
  线索管理列表跟进人验证 : (//p[contains(text(),'管家2')])[1]
  线索管理列表批量分配: //span[contains(text(),'批量分配')]
  线索管理列表重新分配: //a[contains(text(),'重新分配')]
  分配成功验证: (//span[contains(text(),'分配成功')])[1]
  线索管理列表重新分配输入框: "#rc_select_11"
  线索管理列表分配弹窗确定按钮: //span[contains(text(),'确 定')]
  线索管理列表全选: //div[@class='ant-table-selection']//input[@type='checkbox']
  线索管理列表批量分配输入框: (//input[@placeholder='只能输入1-100的整数'])[67]
  线索管理列表线索id: "td.ant-table-cell a.zj-column-value"  # 线索ID值(带链接)
  新详情1线索id验证: "div.basic-cell-value > div:first-child"  # 线索ID值
  新详情1居住地址验证: (//div[contains(text(),'-')])[1]
  新详情1居住姓名验证: //div[contains(text(),'客户名称')]
  新详情1居住创建时间验证: //div[contains(text(),'2025-03-28 17:19:31')]
  新详情1居住手机号验证: //div[contains(text(),'182****3933')]
  新详情1居住来源验证: (//div[contains(text(),'新零售-手工录入-自然进店-自然进店')])[1]
  新详情1居住城市验证: //div[contains(text(),'香港')]
  新详情1居住购车周期验证: //div[contains(text(),'存疑')]
  新详情1居住意向车型验证: (//div[contains(text(),'金杯-新海狮X30L CNG-2023款 70L CNG财富版客车6/7座无空调SWCC15M')])[1]
  新详情1任务列表线索id验证: (//a[@href='javascript:;'][normalize-space()='26780204022329'])[3]
  新详情1任务列表主任务id验证: (//span[normalize-space()='26780204022585'])[1]
  新详情1任务列表任务类型验证: (//span[contains(text(),'待清洗')])[1]
  新详情1任务列表逾期时间验证: (//span[contains(text(),'2025-03-28 22:00:00')])[5]
  新详情1任务列表创建时间验证: (//span[normalize-space()='2025-03-28 17:19:31'])[1]
  新详情1任务列表完成时间验证: (//span[contains(text(),'2025-03-28 17:19:39')])[2]
  新详情1任务列表计划联系时间验证: (//span[contains(text(),'2025-03-28 22:00:00')])[4]
  新详情1任务列表状态验证: (//div)[206]
  新详情1任务列表门店验证: (//span[contains(text(),'加盟商香港综合测试门店1')])[2]
  新详情1任务列表员工验证: (//span[contains(text(),'管家2')])[2]
  新详情1任务列表跟进人验证: (//td[@class='ant-table-cell'][contains(text(),'管家2')])[3]
  新详情2任务列表线索id验证: (//a[@class='fc-blue'][normalize-space()='26421067515981'])[2]
  新详情2任务列表主任务id验证: //span[normalize-space()='26421069103325']
  新详情2任务列表任务类型验证: (//span[contains(text(),'待回电')])[1]
  新详情2任务列表用户诉求验证: (//span[contains(text(),'买车议价')])[1]
  新详情2任务列表逾期时间验证: (//span[contains(text(),'2025-03-12 22:00:00')])[2]
  新详情2任务列表创建时间验证: (//span[contains(text(),'2025-03-12 20:46:20')])[1]
  新详情2任务列表完成时间验证: (//span[contains(text(),'2025-03-12 20:46:22')])[2]
  新详情2任务列表计划联系时间验证: (//span[contains(text(),'2025-03-12 21:00:00')])[2]
  新详情2任务列表状态验证: (//div)[199]
  新详情2任务列表门店验证: (//span[contains(text(),'加盟商香港综合测试门店1')])[2]
  新详情2任务列表员工验证: (//span[contains(text(),'管家2')])[2]
  新详情2任务列表跟进人验证: (//td[@class='ant-table-cell'][contains(text(),'管家2')])[2]
  新详情3任务列表线索id验证: (//a[@href='javascript:;'][normalize-space()='26780204022329'])[1]
  新详情3任务列表主任务id验证: (//span[normalize-space()='26780209257082'])[1]
  新详情3任务列表任务类型验证: (//span[contains(text(),'持续跟进')])[1]
  新详情3任务列表再跟进原因验证: (//span[contains(text(),'-')])[2]
  新详情3任务列表逾期时间验证: (//span[contains(text(),'2025-03-28 22:00:00')])[2]
  新详情3任务列表创建时间验证: (//span[normalize-space()='2025-03-28 17:19:51'])[1]
  新详情3任务列表完成时间验证: (//span[normalize-space()='2025-03-28 17:19:56'])[1]
  新详情3任务列表计划联系时间验证: (//span[contains(text(),'2025-03-28 22:00:00')])[1]
  新详情3任务列表状态验证: (//div)[192]
  新详情3任务列表门店验证: (//span[contains(text(),'系统')])[2]
  新详情3任务列表员工验证: (//span[contains(text(),'系统')])[3]
  新详情3任务列表跟进人验证: (//td[@class='ant-table-cell'][contains(text(),'管家2')])[1]
  新详情4任务列表线索id验证: (//a[@class='fc-blue'][normalize-space()='26780204022329'])[2]
  新详情4任务列表主任务id验证: (//span[normalize-space()='26780206115704'])[1]
  新详情4任务列表任务类型验证: (//span[contains(text(),'待接待')])[1]
  新详情4任务列表任务到店类型验证: (//span[contains(text(),'到门店')])[1]
  新详情4任务列表到店诉求验证: (//span[contains(text(),'现场活动')])[1]
  新详情4任务列表逾期时间验证: (//span[contains(text(),'2025-03-28 22:00:00')])[3]
  新详情4任务列表创建时间验证: (//span[contains(text(),'2025-03-28 17:19:39')])[1]
  新详情4任务列表完成时间验证: (//span[normalize-space()='2025-03-28 17:19:47'])[1]
  新详情4任务列表预约到店时间验证: (//span[normalize-space()='2025-03-28 17:30:00'])[1]
  新详情4任务列表状态验证: (//div)[199]
  新详情4任务列表门店验证: (//span[contains(text(),'加盟商香港综合测试门店1')])[1]
  新详情4任务列表员工验证: (//span[contains(text(),'管家2')])[1]
  新详情4任务列表跟进人验证: (//td[@class='ant-table-cell'][contains(text(),'管家2')])[2]
  新详情对客作业按钮: //div[@id='rc-tabs-0-tab-customWork']
  对客作业信息1验证 : (//span[contains(text(),'任务跟进')])[1]
  对客作业信息2验证 : (//span[contains(text(),'持续跟进')])[2]
  对客作业信息3验证 : (//span[contains(text(),'战败')])[1]
  对客作业信息4验证 : (//span[contains(text(),'管家2')])[3]
  对客作业信息5验证 : (//span[contains(text(),'任务跟进')])[2]
  对客作业信息6验证: (//span[contains(text(),'待接待')])[2]
  对客作业信息7验证: (//span[contains(text(),'待接待')])[2]
  对客作业信息8验证: (//span[contains(text(),'管家2')])[4]
  对客作业信息9验证: (//span[contains(text(),'线索推送')])[1]
  对客作业信息10验证: (//span[@class='content-item-value'][contains(text(),'管家2')])[1]
  对客作业信息11验证: (//span[contains(text(),'金杯-新海狮X30L CNG-2023款 70L CNG财富版客车6/7座无空调SWCC15M')])[1]
  对客作业信息12验证: (//span[contains(text(),'随机经销商')])[1]
  对客作业信息13验证: (//span[contains(text(),'任务跟进')])[3]
  对客作业信息14验证: (//span[contains(text(),'待清洗')])[2]
  对客作业信息15验证: (//span[contains(text(),'有效-预计到店-到门店-现场活动')])[1]
  对客作业信息16验证: (//span[contains(text(),'管家2')])[6]
  对客作业信息17验证: (//span[contains(text(),'26780204022329')])[1]
  对客作业信息18验证: (//span[contains(text(),'客户名称')])[1]
  对客作业信息19验证: (//span[contains(text(),'创建线索')])[1]
  对客作业信息20验证: (//span[contains(text(),'26780204022329')])[2]
  对客作业信息21验证: (//span[contains(text(),'客户名称')])[2]
  对客作业详情信息按钮 : (//span[contains(text(),'详情')])[4]
  对客作业详情信息1验证 : (//span[@class='record-call-value'][normalize-space()='26780204022329'])[1]
  对客作业详情信息2验证 : (//span[@class='record-call-value'][normalize-space()='26780204022585'])[1]
  对客作业详情信息3验证 : (//span[@class='record-call-value'][contains(text(),'待清洗')])[1]
  对客作业详情信息4验证 : (//span[@class='record-call-value'][normalize-space()='2025-03-28 17:19:31'])[1]
  对客作业详情信息5验证 : (//span[normalize-space()='2025-03-28 17:19:32'])[1]
  对客作业详情信息6验证 : (//span[@class='record-call-value'][normalize-space()='2025-03-28 22:00:00'])[1]
  对客作业详情信息7验证 : (//span[@class='record-call-value'][normalize-space()='2025-03-28 22:00:00'])[2]
  对客作业详情信息8验证 : (//span[@class='record-call-value'][normalize-space()='2025-03-28 17:19:39'])[1]
  对客作业详情信息9验证 : (//span[contains(text(),'香港-新零售加盟商测试租户-加盟商香港综合测试门店1')])[1]
  对客作业详情信息10验证 : (//span[@class='record-call-value'][contains(text(),'管家2')])[1]
  对客作业详情信息11验证 : (//span[contains(text(),'存疑')])[1]
  对客作业详情信息12验证 : (//span[@class='record-call-value'][contains(text(),'有效-预计到店-到门店-现场活动')])[1]
  对客作业详情信息13验证 : (//span[normalize-space()='2025-03-28'])[1]




