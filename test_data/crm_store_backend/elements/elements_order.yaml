elements:
  #订单列表
  订单管理一级菜单: //span[@title='订单管理']//span[2]
  订单列表二级菜单: //li[@title='订单列表']//span[@class='ant-pro-menu-item-title'][contains(text(),'订单列表')]
  线索id输入框: (//input[@id='basic_leadId'])[1]
  订单查询按钮: button[class='ant-btn ant-btn-primary'] span
  订单ID: (//td[normalize-space()='93617'])[1]
  线索id: (//a[normalize-space()='25754651876977'])[1]
  客户姓名文本: (//div[contains(text(),'客户姓名：卫星店账号非卫星店订单')])[1]
  手机号文本: (//div[contains(text(),'手机号：151****6033')])[1]
  自然进店单元格: (//td[contains(text(),'自然进店')])[1]
  重点品牌文本: (//div[contains(text(),'重点品牌')])[1]
  已提交文本: (//div[contains(text(),'已提交')])[1]
  审核通过文本: (//div[@class='type-green'])[1]
  已签署文本: (//div[contains(text(),'已签署')])[1]
  车辆型号文本: (//div[contains(text(),'宝马i3 2024款 eDrive 40 L 曜夜运动套装')])[1]
  总价: (//div[contains(text(),'889元')])[1]
  提交人单元格: (//td[contains(text(),'罗秀蕊')])[1]
  日期单元格1: (//td[normalize-space()='2025-02-11 10:37:15'])[1]
  日期单元格2: (//td[normalize-space()='2025-02-11 10:37:08'])[1]
  成交周期: (//div[contains(text(),'0天')])[1]
  已确认单元格: (//div[contains(text(),'已确认')])[1]
  发票校验通过单元格: (//div[contains(text(),'发票校验通过')])[1]
  查看详情: (//a[contains(text(),'查看详情')])[1]
  取消下订: (//a[contains(text(),'取消下订')])[1]
  本地变现: (//a[contains(text(),'本地变现')])[1]
  1条数据: (//li[@class='ant-pagination-total-text'])[1]

  #筛选
  下订时间输入框: (//input[@id='basic_time'])[1]
  日期选择器: (//div[@class='ant-picker-cell-inner'][normalize-space()='15'])[1]
  结束时间输入框: (//input[@placeholder='结束时间'])[1]
  订单审核状态下拉框: (//input[@id='basic_auditStatus'])[1]
  审核通过选项: //div[@title='审核通过']//div[1]
  成交周期下拉框: (//input[@id='basic_transactionCycle'])[1]
  当日转化选项: (//div[@title='当日转化（0）'])[1]
  发票状态下拉框: (//input[@id='basic_invoiceStatus'])[1]
  已确认选项: (//div[@class='ant-select-item-option-content'][contains(text(),'已确认')])[1]
  发票校验状态下拉框: (//input[@id='basic_invoiceAuditStatus'])[1]
  校验通过选项: //div[@title='校验通过']//div[1]
  确认函下拉框: (//input[@id='basic_buyerConfirmStatus'])[1]
  已签署选项: //div[@title='已签署']//div[1]
  品牌类型下拉框: (//input[@id='basic_brandPolicyType'])[1]
  重点品牌选项: //div[@title='重点品牌']//div[1]
  手机号输入框: (//input[@id='basic_phone'])[1]
  订单id: (//td[normalize-space()='90807'])[1]
  订单线索id文本: (//div[contains(text(),'线索id：')])[1]