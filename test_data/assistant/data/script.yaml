test_data:
  test_create_script:
    description: "剧本测试"
    steps:
      - action: click
        selector: 剧本文本
      - action: click
        selector: 开始创作
      - action: fill
        selector: 剧本标题
        value: 剧本标题测试
      - action: click
        selector: 剧本时长
      - action: click
        selector: 剧本时
      - action: click
        selector: 剧本分
      - action: wait
        value: 1
      - action: click
        selector: 剧本时长确定
      - action: click
        selector: 关闭第三个话题
      - action: click
        selector: 关闭第二个话题
      - action: click
        selector: 增加话题
      - action: click
        selector: 关闭第二个话题
      - action: click
        selector: 开始时间输入框
      - action: click
        selector: 话题时
      - action: click
        selector: 话题分
      - action: wait
        value: 1
      - action: click
        selector: 话题时间确定
      - action: click
        selector: 案例引用
      - action: click
        selector: 第一个案例
      - action: fill
        selector: 话题名称
        value: "话题名称测试"
      - action: click
        selector: 生成脚本
      - action: wait
        value: 3
      - action: click
        selector: 重新生成
      - action: click
        selector: 取消重新生成
      - action: click
        selector: 第一个脚本纳入剧本
      - action: click
        selector: 保存完整剧本
  test_script_library:
    description: "剧本库测试"
    steps:
      - action: click
        selector: 剧本文本
      - action: click
        selector: preview_button
      - action: click
        selector: 剧本开始
      - action: click
        selector: 剧本重置
      - action: click
        selector: 剧本刷新
      - action: click
        selector: 关闭预览
      #      - action: pause
      - action: click
        selector: switch_script
      - action: click
        selector: share_button
      - action: click
        selector: share_confirm_button
      - action: wait
        value: 1
      - action: click
        selector: 已共享
      - action: click
        selector: share_confirm_button
      - action: click
        selector: 共享中心
      - action: click
        selector: 我的创作
      - action: wait
        value: 1
      - action: click
        selector: edit_button
      - action: click
        selector: 清空全部
      - use_module: delete_script



