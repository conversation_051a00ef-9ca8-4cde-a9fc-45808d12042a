test_data:
  test_demo:
    description: "demo测试"
    steps:
      - use_module: login_steps
      - action: click
        selector: "图片元素"
      - action: click
        selector: "第二个图片元素"
      - action: click
        selector: "剧本文本"
      - action: click
        selector: "角色图片"
      - action: click
        selector: "开始时间输入框"
      - action: click
        selector: "话题时"
      - action: click
        selector: "话题分"
      - action: click
        selector: "话题时间确定"
      - action: click
        selector: "灵感文本"
      - action: click
        selector: "不保存按钮"
      - action: click
        selector: "成长文本"
      - action: click
        selector: "开始练习按钮"
      - action: click
        selector: "取消练习&考试按钮"
      - action: click
        selector: "考试文本"
      - action: click
        selector: "重新考试按钮"
      - action: click
        selector: "取消练习&考试按钮"


  test_material_open:
    description: "素材显示测试"
    steps:
      - action: click
        selector: 素材
      - action: click
        selector: 素材上传按钮
      - action: click
        selector: 素材常驻素材
      - action: upload
        selector: 素材upload浏览
        value: files/demo/01.PNG
      - action: wait
        value: 1
      - action: click
        selector: 素材确认按钮
      - action: click
        selector: 素材筛选条件选择器输入框
      - action: click
        selector: 素材筛选条件下拉选项-常驻
      - action: click
        selector: 素材查询按钮
      - action: click
        selector: 素材显示开关
      - action: assert_text
        selector: 素材显示结果校验
        value: "常驻素材显示不能超过1个"



  test_material_search:
    description: "素材查询测试"
    steps:
      - action: click
        selector: 素材
      - action: click
        selector: 素材筛选条件选择器输入框
      - action: click
        selector: 素材筛选条件下拉选项-通用
      - action: click
        selector: 素材查询按钮
      - action: assert_text
        selector: 素材筛选条件选择器输入框
        value: "通用素材 "

  test_material_upload:
    description: "素材上传测试"
    steps:
      - action: click
        selector: 素材
      - action: click
        selector: 素材上传按钮
      - action: wait
        value: 2
      - action: click
        selector: 素材通用素材
      - action: wait
        value: 5
      - action: upload
        selector: 素材upload浏览
        value: files/demo/01.PNG
      - action: wait
        value: 1
      - action: click
        selector: 素材确认按钮
      - action: assert_text
        selector: 素材上传成功校验
        value: "成功"


  test_material_del:
    description: "素材删除测试"
    steps:
      - action: click
        selector: 素材
      - action: click
        selector: 素材筛选条件选择器输入框
      - action: click
        selector: 素材筛选条件下拉选项-通用
      - action: click
        selector: 素材查询按钮
      - action: click
        selector: 素材删除按钮
      - action: wait
        value: 1
      - action: click
        selector: 素材删除确认是按钮
      - action: assert_text
        selector: 素材删除成功校验
        value: "删除成功"

  test_material_upload_Failure:
    description: "素材上传失败测试"
    steps:
      - action: click
        selector: 素材
      - action: click
        selector: 素材上传按钮
      - action: click
        selector: 素材常驻素材
      - action: click
        selector: 素材确认按钮
      - action: wait
        value: 1
      - action: assert_text
        selector: 素材未上传文件校验
        value: "请上传文件"
      - action: assert_text
        selector: 素材名称空校验
        value: "限8个字符长度"

  test_Personal_Center:
    description: "个人中心-账号验证"
    steps:
      - action: click
        selector: 头像
      - action: assert_text
        value: "15011290391"
        selector: 用户手机号
  test_Assistant_Settings:
    description: "个人中心-AI助手设置"
    steps:
      - action: click
        selector: 头像
      - action: click
        selector: 用户手机号
      - action: click
        selector: 用户名
      - action: click
        selector: AI助手设置
      - action: click
        selector: 大字号选项
      - action: click
        selector: 默认字号选项
      - action: click
        selector: 综合信息选项
      - action: click
        selector: 综合信息选项
      - action: click
        selector: 大V观点选项
      - action: click
        selector: 大V观点选项
      - action: click
        selector: 深度解析选项
      - action: click
        selector: 深度解析选项
      - action: click
        selector: 自动解答开关
      - action: click
        selector: 自动解答开关
      - action: click
        selector: 城市选项
      - action: wait
        value: 1
      - action: click
        selector: 安庆选项
      - action: assert_text
        value: "安庆"
        selector: 安庆选项

  test_skill_Failure:
    description: "技巧新增失败"
    steps:
      - action: click
        selector: 技巧菜单项
      - action: click
        selector: 技巧新增按钮
      - action: click
        selector: 技巧保存按钮
      - action: assert_text
        value: "保 存"
        selector: 技巧保存按钮
      - action: assert_text
        value: "用户已存在该类型的技巧"
        selector: 技巧失败提示

  test_skill_Enable:
    description: "技巧启用"
    steps:
      - action: click
        selector: 技巧菜单项
      - action: click
        selector: 技巧编辑
      - action: wait
        value: 1
      - action: click
        selector: 技巧启用技巧开关
      - action: wait
        value: 1
      - action: click
        selector: 技巧启用技巧开关
      - action: wait
        value: 1
      - action: click
        selector: 技巧保存按钮
      - action: assert_text
        value: "保存成功"
        selector: 技巧提示

  test_skill_speech:
    description: "技巧话术启用"
    steps:
      - action: click
        selector: 技巧菜单项
      - action: click
        selector: 技巧编辑
      - action: wait
        value: 1
      - action: click
        selector: 技巧展示话术开关
      - action: wait
        value: 1
      - action: click
        selector: 技巧展示话术开关
      - action: wait
        value: 1
      - action: click
        selector: 技巧保存按钮
      - action: assert_text
        value: "保存成功"
        selector: 技巧提示

  test_Case_search:
    description: "灵感案例搜索"
    steps:
      - action: click
        selector: 灵感菜单项
      - action: click
        selector: 灵感搜索输入框
      - action: fill
        selector: 灵感搜索输入框
        value: "陆放车型实质上是什么车型"
      - action: click
        selector: 灵感搜索按钮
      - action: click
        selector: 灵感搜索结果
      - action: wait
        value: 1
      - action: assert_text
        selector: 灵感详情标题
        value: "陆放车型实质上是什么车型？"
  test_Script_search:
    description: "灵感脚本搜索"
    steps:
      - action: click
        selector: 灵感菜单项
      - action: click
        selector: 灵感脚本tab
      - action: click
        selector: 灵感搜索输入框
      - action: fill
        selector: 灵感搜索输入框
        value: "22222"
      - action: click
        selector: 灵感搜索按钮
      - action: wait
        value: 1
      - action: assert_text
        selector: 灵感脚本搜索空验证
        value: "未查询到相关内容，请重新输入关键词"



  test_Script_Failure:
    description: "脚本制作失败"
    steps:
      - action: click
        selector: 灵感菜单项
      - action: click
        selector: 灵感制作脚本
      - action: click
        selector: 灵感确定
      - action: assert_text
        selector: 灵感失败提示
        value: "请选择引用的原文"

  test_Case_Screening:
    description: "筛选组件"
    steps:
      - action: click
        selector: 灵感菜单项
      - action: click
        selector: 灵感筛选组件
      - action: click
        selector: 灵感筛选问题意图
      - action: wait
        value: 1
      - action: fill
        selector: 灵感筛选问题意图
        value: "找车"
      - action: click
        selector: 灵感筛选找车
      - action: click
        selector: 灵感筛选达人名称
      - action: fill
        selector: 灵感筛选达人名称
        value: "唯爱丰田TOYOTA"
      - action: click
        selector: 灵感筛选唯爱丰田TOYOTA
      - action: click
        selector: 灵感筛选讲解车系
      - action: fill
        selector: 灵感筛选讲解车系
        value: "丰田"
      - action: click
        selector: 灵感筛选丰田
      - action: click
        selector: 灵感筛选车系
      - action: fill
        selector: 灵感筛选车系
        value: "汉兰达"
      - action: click
        selector: text=汉兰达
      - action: click
        selector: 灵感确定
      - action: click
        selector: 灵感搜索结果
      - action: wait
        value: 1
      - action: assert_text
        selector: 灵感详情标题
        value: "汉兰达多少钱？10万以内性价比最高的车型是什么？"


  test_growth_fail_practice:
    description: "成长练习失败"
    steps:
      - action: click
        selector: 成长导航栏链接
      - action: click
        selector: 成长开始练习按钮
      - action: click
        selector: 成长确定按钮
      - action: wait
        value: 1
      - action: assert_text
        value: "请开启麦克风权限"
        selector: 成长失败提示
      - action: click
        selector: 成长取消按钮

  test_growth_fail_exam:
    description: "成长考试失败"
    steps:
      - action: click
        selector: 成长导航栏链接
      - action: click
        selector: 成长考试tab
      - action: click
        selector: 成长重新考试按钮
      - action: click
        selector: 成长考试开始按钮
      - action: wait
        value: 1
      - action: assert_text
        value: "请开启麦克风权限"
        selector: 成长失败提示
      - action: click
        selector: 成长取消按钮

  test_growth_practice_search:
    description: "成长练习筛选"
    steps:
      - action: click
        selector: 成长导航栏链接
      - action: click
        selector: 成长开始日期输入框
      - action: fill
        selector: 成长开始日期输入框
        value: "2023-03-01 00:00"
      - action: click
        selector: 成长日期组件确定按钮
      - action: click
        selector: 成长结束日期输入框
      - action: fill
        selector: 成长结束日期输入框
        value: "2023-12-01 00:00"
      - action: click
        selector: 成长日期组件确定按钮
      - action: wait
        value: 1
      - action: assert_text
        value: "无练习记录"
        selector: 成长练习无记录

  test_growth_practice:
    description: "成长练习详情"
    steps:
      - action: click
        selector: 成长导航栏链接
      - action: click
        selector: 练习活动项图标
      - action: assert_text
        value: "直播文本"
        selector: 成长练习回放验证
      - action: click
        selector: 数据概览
      - action: assert_text
        value: "发言占比"
        selector: 成长数据概览验证

  test_growth_exam:
    description: "成长考试详情"
    steps:
      - action: click
        selector: 成长导航栏链接
      - action: click
        selector: 成长考试tab
      - action: click
        selector: 考试查看按钮
      - action: assert_text
        value: "直播文本"
        selector: 成长练习回放验证
      - action: click
        selector: 数据概览
      - action: assert_text
        value: "发言占比"
        selector: 成长考试数据概览验证
      - action: click
        selector: 考试关闭按钮

  test_script_creation_1:
    description: "剧本创作-案例引用"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: 剧本开始创作
      - action: click
        selector: 剧本时长
      - action: click
        selector: text="05"
      - action: click
        selector: 剧本时长确定
      - action: click
        selector: 剧本案例引用
      - action: click
        selector: text="如何查看车型的报价单？"
      - action: click
        selector: 剧本开始时间
      - action: click
        selector: "话题时"
      - action: click
        selector: "话题分"
      - action: click
        selector: "话题时间确定"
      - action: click
        selector: 剧本话题名称
      - action: fill
        selector: 剧本话题名称
        value: "话题名称自动化案例引用"
      - action: click
        selector: 剧本生成脚本
      - action: wait
        value: 40
      - action: click
        selector: 第一个脚本纳入剧本
      - action: click
        selector: 关闭第三个话题
      - action: click
        selector: 关闭第二个话题
      - action: wait
        value: 1
      - action: click
        selector: 剧本保存完整剧本
      - action: assert_text
        value: "剧本保存成功"
        selector: 剧本保存后验证

  test_script_creation_2:
    description: "剧本创作-脚本引用"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: 剧本开始创作
      - action: click
        selector: 剧本时长
      - action: click
        selector: text="05"
      - action: click
        selector: 剧本时长确定
      - action: click
        selector: 剧本脚本引用
      - action: click
        selector: 第一个案例
      - action: click
        selector: 剧本开始时间
      - action: click
        selector: "话题时"
      - action: click
        selector: "话题分"
      - action: click
        selector: "话题时间确定"
      - action: click
        selector: 剧本话题名称
      - action: fill
        selector: 剧本话题名称
        value: "话题名称自动化脚本引用"
      - action: click
        selector: 关闭第三个话题
      - action: click
        selector: 关闭第二个话题
      - action: wait
        value: 1
      - action: click
        selector: 剧本保存完整剧本
      - action: assert_text
        value: "剧本保存成功"
        selector: 剧本保存后验证

  test_script_creation_3:
    description: "剧本创作-手动录入"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: 剧本开始创作
      - action: click
        selector: 剧本时长
      - action: click
        selector: text="05"
      - action: click
        selector: 剧本时长确定
      - action: click
        selector: 剧本手动录入
      - action: click
        selector: 剧本开始时间
      - action: click
        selector: "话题时"
      - action: click
        selector: "话题分"
      - action: click
        selector: "话题时间确定"
      - action: click
        selector: 剧本话题名称
      - action: fill
        selector: 剧本话题名称
        value: "话题名称自动化手动录入"
      - action: click
        selector: 关闭第三个话题
      - action: click
        selector: 关闭第二个话题
      - action: click
        selector: optional_script_1_textarea
      - action: fill
        selector: optional_script_1_textarea
        value: "手动录入文本"
      - action: wait
        value: 1
      - action: click
        selector: modal_save_button
      - action: click
        selector: 剧本保存完整剧本
      - action: assert_text
        value: "剧本保存成功"
        selector: 剧本保存后验证

  test_script_open:
    description: "剧本创作启用"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: switch_script
      - action: assert_text
        value: "启用成功，已展示到直播栏目底部"
        selector: 剧本启用验证
      - action: wait
        value: 10
      - action: click
        selector: switch_script

  test_script_del_topics:
    description: "剧本创作-手动录入"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: 剧本开始创作
      - action: click
        selector: 剧本手动录入2
      - action: click
        selector: 关闭第二个话题
      - action: assert_text
        value: "确认要删除该话题吗？"
        selector: 剧本第二话题删除验证
      - action: click
        selector: 删除剧本确定按钮

  test_share_pre:
    description: "剧本共享预览"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: 共享中心
      - action: wait
        value: 2
      - action: click
        selector: 剧本共享预览
      - action: assert_text
        value: "关闭"
        selector: 剧本共享预览关闭
      - action: wait
        value: 2
      - action: click
        selector: 剧本共享预览关闭

  test_script_pre:
    description: "剧本创作预览"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: preview_button
      - action: assert_text
        value: "关闭"
        selector: 剧本共享预览关闭
      - action: wait
        value: 2
      - action: click
        selector: 剧本共享预览关闭



  test_script_add_topics:
    description: "剧本创作-增加话题"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: 剧本开始创作
      - action: wait
        value: 1
      - action: click
        selector: 剧本增加话题
      - action: assert_text
        value: "04"
        selector: 剧本增加话题验证

  test_script_clear_all:
    description: "剧本创作-清空全部"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: 剧本开始创作
      - action: wait
        value: 1
      - action: click
        selector: 剧本增加话题
      - action: click
        selector: 剧本手动录入
      - action: click
        selector: 剧本开始时间
      - action: click
        selector: "话题时"
      - action: click
        selector: "话题分"
      - action: click
        selector: "话题时间确定"
      - action: click
        selector: 剧本话题名称
      - action: fill
        selector: 剧本话题名称
        value: "话题名称自动化脚本引用"
      - action: click
        selector: 剧本清空全部
      - action: click
        selector: 剧本编辑不保存
      - action: 验证可见
        selector: 剧本开始创作

  test_script_leave:
    description: "剧本创作中离开页面"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: 剧本开始创作
      - action: wait
        value: 1
      - action: click
        selector: 剧本增加话题
      - action: click
        selector: 剧本手动录入
      - action: click
        selector: 剧本开始时间
      - action: click
        selector: "话题时"
      - action: click
        selector: "话题分"
      - action: click
        selector: "话题时间确定"
      - action: click
        selector: 剧本话题名称
      - action: fill
        selector: 剧本话题名称
        value: "话题名称自动化脚本引用"
      - action: click
        selector: 素材
      - action: wait
        value: 1
      - action: 验证可见
        selector: 剧本编辑中离开验证
      - action: assert_text
        value: "检测到当前剧本未保存，离开前，请确认是否保存当前更新的内容？"
        selector: 剧本编辑中离开验证
      - action: click
        selector: 剧本编辑不保存

  test_script_failure:
    description: "剧本创作失败"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: 剧本开始创作
      - action: wait
        value: 1
      - action: click
        selector: 剧本保存完整剧本
      - action: assert_text
        value: "请选择时长"
        selector: 剧本空保存验证

  test_script_edit:
    description: "剧本创作-重新编辑"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: edit_button
      - action: click
        selector: 剧本开始时间
      - action: click
        selector: "话题时"
      - action: click
        selector: "话题分"
      - action: click
        selector: "话题时间确定"
      - action: click
        selector: 剧本话题名称
      - action: fill
        selector: 剧本话题名称
        value: "再次编辑标题"
      - action: click
        selector: 剧本再次编辑
      - action: click
        selector: optional_script_1_textarea
      - action: fill
        selector: optional_script_1_textarea
        value: "再次编辑测试"
      - action: wait
        value: 1
      - action: click
        selector: modal_save_button
      - action: click
        selector: 剧本保存完整剧本
      - action: assert_text
        value: "剧本保存成功"
        selector: 剧本保存后验证

  test_script_del:
    description: "剧本创作-删除"
    steps:
      - action: click
        selector: 剧本菜单
      - action: click
        selector: delete_button
      - action: assert_text
        value: "删除后无法恢复，确定删除该剧本吗？"
        selector: 删除删除提示文本
      - action: wait
        value: 2
      - action: click
        selector: 删除剧本确定按钮
      - action: assert_text
        value: "删除成功"
        selector: 剧本删除成功提示验证