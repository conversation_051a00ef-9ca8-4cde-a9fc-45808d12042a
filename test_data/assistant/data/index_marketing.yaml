test_data:
  test_zong_kai_guan:
    description: "营销助手页面总开关测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: AI营销助手工作中
      - action: assert_text
        value: "未开启"
        selector: 未开启
      - action: click
        selector: AI营销助手未开启
      - action: assert_text
        value: "工作中"
        selector: 已开启
  test_jin_si:
    description: "营销助手页面进私触达测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: wait
        value: 2
      - action: click
        selector: 进私开关
      - action: click
        selector: 进私开关
      - action: assert_text
        value: "生效中"
        selector: 已生效
  test_qian_ke:
    description: "营销助手页面潜客触达测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: wait
        value: 2
      - action: click
        selector: 潜客开关
      - action: click
        selector: 潜客开关
      - action: assert_text
        value: "生效中"
        selector: 已生效
  test_wan_hui:
    description: "营销助手页面线索挽回测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: wait
        value: 2
      - action: click
        selector: 挽回开关
      - action: click
        selector: 挽回开关
      - action: assert_text
        value: "两次动态挽回生效中"
        selector: 挽回已生效
  test_hui_fu_lv:
    description: "营销助手页面回复率提升测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: wait
        value: 2
      - action: click
        selector: 回复率开关
      - action: click
        selector: 回复率开关
      - action: assert_text
        value: "3分钟回复率提升生效中"
        selector: 回复率提升已生效
  test_si_xin_hui_hua:
    description: "营销助手页面私信会话展开测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 私信会话
      - action: assert_text
        value: "收起会话"
        selector: 收起会话
      - action: click
        selector: 收起会话
  test_liu_zi_zhuang_tai:
    description: "营销助手页面私信会话弹窗留资状态筛选测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 私信会话
      - action: assert_text
        value: "收起会话"
        selector: 收起会话
      - action: click
        selector: 全部
      - action: click
        selector: 未留资
      - action: assert_text
        value: "未留资"
        selector: 留资筛选
  test_hui_fu_zhuang_tai:
    description: "营销助手页面私信会话弹窗回复状态筛选测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 私信会话
      - action: click
        selector: 全部状态
      - action: click
        selector: 未回复
      - action: assert_text
        value: "未回复"
        selector: 没回复
  test_qi_zhi_shi_jian:
    description: "营销助手页面私信会话弹窗起止时间筛选测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 私信会话
      - action: click
        selector: 起止时间
      - action: fill
        selector: 起止时间
        value: "2025-03-01"
      - action: click
        selector: 结束日期
      - action: fill
        selector: 结束日期
        value: "2025-03-07"
  test_chong_zhi:
    description: "营销助手页面私信会话弹窗重置测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 私信会话
      - action: click
        selector: 全部状态
      - action: click
        selector: 未回复
      - action: assert_text
        value: "未回复"
        selector: 没回复
      - action: click
        selector: 重置按钮
      - action: assert_text
        value: "全部"
        selector: 全部
  test_hui_hua_tab:
    description: "营销助手页面私信会话弹窗会话列表tab测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 私信会话
      - action: click
        selector: 转人工会话
      - action: click
        selector: 全部会话
      - action: assert_text
        value: "全部"
        selector: 全部
  test_shi_fou_liu_zi:
    description: "私信营销页面是否留资筛选测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 私信营销
      - action: click
        selector: 全部
      - action: click
        selector: 未留资
      - action: assert_text
        value: "未留资"
        selector: 未留资
  test_chu_da_zhuang_tai:
    description: "私信营销页面触达状态筛选测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 私信营销
      - action: click
        selector: 触达状态
      - action: click
        selector: 已触达未回复
      - action: click
        selector: 查询按钮
      - action: monitor_request
        url_pattern: /api/aim/reach/user/list/page
        action_type: click
        selector: 查询按钮
        assert_params:
          $.reachStatus: "1"
  test_suo_shu_di_qu:
    description: "私信营销页面所属地区筛选测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 私信营销
      - action: click
        selector: 所属地区
      - action: click
        selector: 北京
      - action: assert_text
        value: "北京"
        selector: 北京
  test_yi_xiang_che_xi:
    description: "私信营销页面意向车系筛选测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 私信营销
      - action: click
        selector: 所属地区
      - action: click
        selector: 意向车系输入框
      - action: fill
        selector: 意向车系输入框
        value: "宝马"
      - action: click
        selector: 查询按钮
      - action: monitor_request
        url_pattern: /api/aim/reach/user/list/page
        selector: 查询按钮
        action_type: click
        assert_params:
          $.preferSeriesName: "宝马"
  test_si_xin_chong_zhi:
    description: "私信营销页面重置按钮测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 私信营销
      - action: click
        selector: 触达状态
      - action: click
        selector: 已触达未回复
      - action: click
        selector: 所属地区
      - action: click
        selector: 北京
      - action: click
        selector: 意向车系输入框
      - action: fill
        selector: 意向车系输入框
        value: "宝马"
      - action: click
        selector: 查询按钮
      - action: click
        selector: 私信重置
      - action: monitor_request
        url_pattern: /api/aim/reach/user/list/page
        selector: 私信重置
        action_type: click
        assert_params:
          $.preferSeriesName: null
  test_si_xin_ri_qi:
    description: "私信营销页面日期组件测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 私信营销
      - action: click
        selector: 私信开始日期
      - action: fill
        selector: 私信开始日期
        value: "2025-04-01 19:43"
      - action: click
        selector: 结束日期
      - action: fill
        selector: 结束日期
        value: "2025-04-07 19:43"
      - action: click
        selector: 查询按钮
  test_xian_suo_zong_lan:
    description: "线索管理页面线索总览测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 线索管理
      - action: assert_text
        value: "全部线索数"
        selector: 全部线索数
  test_xian_suo_ri_qi:
    description: "线索管理页面日期组件测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 线索管理
      - action: click
        selector: 30天
      - action: assert_text
        value: "30天"
        selector: 30天
  test_xian_suo_lie_biao:
    description: "线索管理页面列表展示测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 线索管理
      - action: wait
      - action: monitor_response
        url_pattern: api/aim/leads/page?BUserId=9250&pageIndex=1&pageSize=10
        selector: 关闭图标
        action_type: click
        assert_params:
          $.result.list[0].sourceName: "抖音"
  test_zhi_neng_hui_fu:
    description: "营销助手页面弹幕智能回复开关测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 弹幕智能回复开关
      - action: click
        selector: 弹幕智能回复开关
      - action: assert_text
        value: "弹幕智能回复生效中"
        selector: 弹幕智能回复生效中
  test_she_zhi_yu_liao:
    description: "营销助手页面设置语料入口测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 设置语料
      - action: assert_text
        value: "回复语料设置 新增"
        selector: 回复语料设置
  test_xin_zeng_yu_liao:
    description: "回复语料设置页面新增功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 设置语料
      - action: click
        selector: 新增
      - action: assert_text
        value: "新增弹幕语料"
        selector: 新增弹幕语料
  test_xin_zeng_guan_jian_zi:
    description: "新增弹幕语料页面新增并删除关键字功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 设置语料
      - action: click
        selector: 新增
      - action: click
        selector: 关键字输入框
      - action: fill
        selector: 关键字输入框
        value: "驾照"
      - action: keyboard_press
        value: Enter
      - action: assert_text
        value: "驾照"
        selector: 驾照
      - action: click
        selector: 关键字标签
  test_xin_zeng_danmu_hui_fu_nei_rong:
    description: "新增弹幕语料页面新增并删除回复内容测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 设置语料
      - action: click
        selector: 新增
      - action: click
        selector: 弹幕回复输入框
      - action: fill
        selector: 弹幕回复输入框
        value: "点击关注"
      - action: keyboard_press
        value: Enter
      - action: assert_text
        value: "点击关注"
        selector: 点击关注
      - action: click
        selector: 已添加回复标签
  test_que_ding_xin_zeng:
    description: "新增一条弹幕语料功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 设置语料
      - action: click
        selector: 新增
      - action: click
        selector: 关键字输入框
      - action: fill
        selector: 关键字输入框
        value: "驾照"
      - action: keyboard_press
        value: Enter
      - action: click
        selector: 弹幕回复输入框
      - action: fill
        selector: 弹幕回复输入框
        value: "点击关注"
      - action: keyboard_press
        value: Enter
      - action: monitor_response
        url_pattern: /api/aim/aimDanmakuReply/saveOrUpdate
        selector: 确定
        action_type: click
        assert_params:
          $.result: "True"
#      - action: wait
#      - action: monitor_response
#        url_pattern: /api/aim/aimDanmakuReply/list?pageNum=1&pageSize=10
#        selector: 删除
#        save_params:
#          $.result.total: Total
  test_fan_ye:
    description: "弹幕语料页面翻页功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 设置语料
      - action: double_click
        selector: 翻页
      - action: assert_visible
        selector: 序号
  test_bian_ji_yu_liao:
    description: "弹幕语料页面编辑功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 设置语料
      - action: click
        selector: 编辑
      - action: assert_text
        value: "编辑弹幕语料"
        selector: 编辑弹幕语料
  test_shan_chu_yu_liao:
    description: "弹幕语料页面删除功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 设置语料
      - action: click
        selector: 删除
      - action: click
        selector: 确认
  test_bai_ming_dan:
    description: "白名单入口功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 白名单菜单项
      - action: assert_text
        value: "白名单 新增"
        selector: 白名单
  test_xin_zeng_bai_ming_dan:
    description: "白名单新增功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 白名单菜单项
      - action: click
        selector: 新增
      - action: click
        selector: 抖音昵称输入框
      - action: fill
        selector: 抖音昵称输入框
        value: "噼里啪啦"
      - action: click
        selector: 备注输入框
      - action: fill
        selector: 备注输入框
        value: "加入白名单"
      - action: monitor_response
        url_pattern: /api/aim/aimDanmakuWhitelist/saveOrUpdate
        selector: 确定
        action_type: click
        assert_params:
          $.result: "True"
  test_bian_ji_bai_ming_dan:
    description: "白名单编辑功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 白名单菜单项
      - action: click
        selector: 编辑
      - action: assert_text
        value: "编辑白名单账号"
        selector: 编辑白名单
  test_shan_chu_bai_ming_dan:
    description: "白名单删除功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 白名单菜单项
      - action: click
        selector: 删除白名单
      - action: monitor_response
        url_pattern: /api/aim/aimDanmakuWhitelist/delete/*
        selector: 确认
        action_type: click
        assert_params:
          $.result: "True"
  test_fu_dai_kou_ling:
    description: "福袋口令入口功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 福袋口令菜单
      - action: assert_text
        value: "编辑白名单账号"
        selector: 编辑白名单
  test_xin_zeng_fu_dai:
    description: "福袋口令新增功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 福袋口令菜单
      - action: click
        selector: 新增
      - action: click
        selector: 口令输入框
      - action: fill
        selector: 口令输入框
        value: "点点关注"
      - action: click
        selector: 备注输入框
      - action: fill
        selector: 备注输入框
        value: "不进行智能回复"
      - action: monitor_response
        url_pattern: /api/aim/aimFortuneBlock/saveOrUpdate
        selector: 确定
        action_type: click
        assert_params:
          $.result: "True"
  test_bian_ji_fu_dai:
    description: "福袋口令编辑功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 福袋口令菜单
      - action: click
        selector: 编辑
      - action: assert_text
        value: "编辑屏蔽福袋口令"
        selector: 编辑对话框
  test_shan_chu_fu_dai:
    description: "福袋口令删除功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 福袋口令菜单
      - action: click
        selector: 删除福袋
      - action: monitor_response
        url_pattern: /api/aim/aimFortuneBlock/delete/*
        selector: 确认
        action_type: click
        assert_params:
          $.result: "True"
  test_dan_mu_xun_huan_bo_bao:
    description: "弹幕循环播报开关功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 弹幕循环播报开关
      - action: assert_text
        value: "弹幕循环播报生效中"
        selector: 弹幕循环播报生效中
      - action: click
        selector: 弹幕循环播报开关
  test_dan_mu_xun_huan_ru_kou:
    description: "弹幕循环播报入口功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 去设置
      - action: assert_text
        value: "循环播报设置"
        selector: 循环播报设置
  test_xin_zeng_bo_bao:
    description: "弹幕循环播报新增功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 去设置
      - action: click
        selector: 轮播内容输入框
      - action: fill
        selector: 轮播内容输入框
        value: "谢谢点点关注"
      - action: keyboard_press
        value: Enter
      - action: assert_text
        value: "谢谢点点关注"
        selector: 谢谢点点关注
      - action: click
        selector: 确定
      - action: wait
  test_shan_chu_bo_bao:
    description: "弹幕循环播报删除功能测试"
    steps:
      - use_module: login_futian
      - action: click
        selector: 营销
      - action: click
        selector: 营销助手
      - action: click
        selector: 去设置
      - action: click
        selector: 轮播内容输入框
      - action: click
        selector: 删除图标
      - action: click
        selector: 确认按钮
      - action: click
        selector: 确定
      - action: wait

















