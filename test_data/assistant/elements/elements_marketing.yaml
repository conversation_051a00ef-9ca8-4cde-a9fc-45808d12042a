elements:
  用户名输入框: role=textbox[name="请输入您的用户名"]
  密码输入框: role=textbox[name="请输入您的密码"]
  营销: text=营销
  营销助手: text=营销助手
  AI营销助手工作中: "div >> text=/^AI营销助手工作中$/ >> role=switch"
  AI营销助手未开启: "div >> text=/^AI营销助手未开启$/ >> role=switch"
  进私开关: //*[@id="app"]/main/div/div[2]/div[2]/section/div[2]/div/div[1]/div[2]/button
  未开启: text=未开启
  已开启: text=工作中
  已生效: text=生效中
  潜客开关: "div:nth-child(2) > .item > .item-right"
  挽回开关: "div:nth-child(3) > .item > .item-right"
  挽回已生效: text=两次动态挽回生效
  回复率开关: "div:nth-child(4) > .item > .item-right"
  回复率提升已生效: text=3分钟回复率提升生效
  私信会话: "text=私信会话"
  收起会话: text=收起会话
  私信营销: "text=私信营销"
  全部: "text=全部"
  全部状态: "div:nth-child(2) > div:nth-child(2) > .ant-form-item-control-input > .ant-form-item-control-input-content > .ant-select > .ant-select-selector"
  未留资: ".rc-virtual-list-holder-inner > div:nth-child(2)"
  未回复: ".rc-virtual-list-holder-inner > div:nth-child(2)"
  留资筛选: (//span[@class='ant-select-selection-item'])[1]
  没回复: text=未回复
  起止时间: role=textbox[name="起止时间 :"]
  开始日期: get_by_title("-03-01")
  结束日期: role=textbox[name="结束日期"]
  结束日期选择: role=textbox[name="结束日期"] >> title=-03-07 >> div
  重置按钮: role=button[name="重置"]
  转人工会话: "text=转人工会话"
  全部会话: "text=全部会话"
  触达状态: "div:nth-child(2) > div:nth-child(2) > .ant-form-item-control-input > .ant-form-item-control-input-content > .ant-select > .ant-select-selector"
  已触达未回复: ".rc-virtual-list-holder-inner > div:nth-child(3)"
  断言已触达未回复: 'locator("#app div").filter(has_text=re.compile(r"^已触达未回复$"))'
  所属地区: //*[@id="app"]/main/div/div[2]/div[2]/form/div[1]/div[3]/div[2]/div/div/div/div/span[2]
  北京: "text=北京"
  意向车系输入框: "role=textbox[name='意向车系 :']"
  查询按钮: "role=button[name='查 询']"
  私信重置: //*[@id="app"]/main/div/div[2]/div[2]/form/div[2]/button[1]/span
  私信开始日期: //*[@id="app"]/main/div/div[2]/div[2]/div[1]/div[2]/div/div/div[1]/input
  线索管理: "text=线索管理"
  全部线索数: "text=全部线索数"
  30天: "text=30天"
  关闭图标: "role=img[name='close-circle'] >> svg"
  弹幕智能回复开关: "div:nth-child(4) > div > .item > .item-right"
  弹幕智能回复生效中: "text=弹幕智能回复生效中"
  设置语料: "text=设置语料"
  回复语料设置: "text=回复语料设置 新增"
  新增: "text=新增"
  新增弹幕语料: "text=新增弹幕语料"
  关键字输入框: "role=textbox[name='请输入关键字，最多10个字']"
  回车提示区域: 'class="<div data-v-a861faf4="" class="add-btn">新增</div>"'
  关键字标签: "span:has-text('驾照') >> span"
  驾照: "text=驾照"
  弹幕回复输入框: "role=textbox[name='请输入弹幕回复内容，最多27个字']"
  回复内容操作区: "div:has-text(/^点击“回车”增加多条回复内容 新增$/) >> div"
  已添加回复标签: "span:has-text('点击关注') >> span"
  点击关注: "text=点击关注"
  确定: "role=button[name='确 定']"
  关键字: "role=cell[name='驾照']"
  删除语料: "role=row[name='驾照 点击关注 编辑 删除'] >> div >> nth=2"
  翻页: "role=button[name='right']"
  序号: "role=cell[name='21']"
  编辑: "text=编辑"
  编辑弹幕语料: "text=编辑弹幕语料"
  删除: (//div[@class='edit-btn delete'][contains(text(),'删除')])[${Total}]
  确认: "role=button[name='确 认']"
  白名单菜单项: "text=白名单"
  抖音昵称输入框: "role=textbox[name='抖音昵称 *']"
  备注输入框: "role=textbox[name='备注']"
  白名单: "text=白名单 新增"
  编辑白名单: "text=编辑白名单账号"
  删除白名单: "text=删除"
  福袋口令菜单: "text=福袋口令"
  口令输入框: "role=textbox[name='福袋口令 *']"
  编辑行: "role=row[name='vvvv - 2025-04-10 11:30:07 编辑 删除'] >> div >> nth=1"
  编辑对话框: "text=编辑屏蔽福袋口令"
  关闭按钮: "role=button[name='Close']"
  删除行: "role=row[name='vvvv - 2025-04-10 11:30:07 编辑 删除'] >> div >> nth=2"
  删除福袋: "text=删除"
  弹幕循环播报开关: "div:nth-child(4) > div:nth-child(2) > .item > .item-right"
  弹幕循环播报生效中: "text=弹幕循环播报生效中"
  去设置: "text=去设置"
  循环播报设置: "text=循环播报设置"
  轮播内容输入框: "role=textbox[name='请编辑轮播内容，上限49个字']"
  谢谢点点关注: "text=谢谢点点关注"
  删除图标: "role=row[name='谢谢点点关注 删除'] >> role=img"
  确认按钮: "role=tooltip >> role=button[name='确 定']"