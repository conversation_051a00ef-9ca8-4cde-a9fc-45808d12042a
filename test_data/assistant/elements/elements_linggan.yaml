elements:
  灵感: "text=灵感"
  输入框: "input[type='text']"
  搜索图标: ".search-icon"
  筛选图标: ".filter-icon"
  开播时间: "role=textbox[name='开播时间']"
  日期_02_01: "role=textbox[name='开播时间'] >> text=-02-01 >> div"
  日期: "ant-picker-cell-inner"
  日期_02_07: "role=textbox[name='结束日期'] >> text=-02-07 >> div"
  开播时间输入框: role=textbox[name="开播时间"]
  结束日期输入框: role=textbox[name="结束日期"]
  确定按钮: "role=button[name='确 定']"
  问题意图: "role=combobox[name='问题意图']"
  找车: "text=找车"
  达人名称: "role=combobox[name='达人名称']"
  车圈大表姐: "text=车圈大表姐🐼"
  讲解车系: "role=combobox[name='讲解车系']"
  Abarth: "text=Abarth"
  车系ID输入框: "#form_item_seriesId"
  Abarth_500新能源: "text=Abarth 500新能源"
  收藏案例: "text=收藏案例"
  确定: "text=确定"
  未查询到内容: "div:has-text('找案例找脚本 未查询到相关内容，请重新输入关键词') >> span >> nth=1"
  取消: "text=取消"
  重置: "text=重置"
  找脚本: "text=找脚本"
  制作脚本: "text=制作脚本"
  找案例: "text=找案例"
  无收藏: ".no-collection >> nth=0"
  更新时间: role=textbox[name="更新时间"]
  排行榜: text="排行榜"
  创建人: role=combobox[name="创建人"]
  东雪: text="东雪" >> exact=True
  最小值: role=spinbutton[name="最小值"]
  最大值: role=spinbutton[name="最大值"]
  车系ID: "#form_item_seriesId"
  张鹏飞: "text=张鹏飞"
