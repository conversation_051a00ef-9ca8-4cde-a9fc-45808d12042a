from typing import Any
from playwright.sync_api import Page
from constants import DEFAULT_TIMEOUT, DEFAULT_TYPE_DELAY

# 注意：这个模块中的函数将来需要能够访问一个统一的定位器解析器，
# 现在暂时假设 selector 已经是 Playwright 可以直接使用的字符串。

def click(page: Page, selector: str):
    """点击元素"""
    page.locator(selector).first.click(timeout=DEFAULT_TIMEOUT)

def fill(page: Page, selector: str, value: Any):
    """在输入框中填写文本，支持数字类型"""
    # 确保传递给 Playwright 的 fill 方法的值是字符串类型
    if value is not None:
        value = str(value)
    page.locator(selector).fill(value)

def press_key(page: Page, selector: str, key: str):
    """在元素上按键"""
    page.locator(selector).press(key)

def upload_file(page: Page, selector: str, file_path: str):
    """上传文件"""
    page.locator(selector).set_input_files(file_path)

def hover(page: Page, selector: str):
    """鼠标悬停"""
    page.locator(selector).hover()

def double_click(page: Page, selector: str):
    """双击元素"""
    page.locator(selector).dblclick()

def right_click(page: Page, selector: str):
    """右键点击元素"""
    page.locator(selector).click(button="right")

def select_option(page: Page, selector: str, value: str):
    """通过值选择下拉选项"""
    page.locator(selector).select_option(value)

def drag_and_drop(page: Page, source_selector: str, target_selector: str):
    """拖放元素"""
    page.locator(source_selector).drag_to(page.locator(target_selector))

def focus(page: Page, selector: str):
    """聚焦元素"""
    page.locator(selector).focus()

def blur(page: Page, selector: str):
    """使元素失焦"""
    page.locator(selector).blur()

def type_text(page: Page, selector: str, text: str, delay: int = DEFAULT_TYPE_DELAY):
    """模拟键盘输入"""
    page.locator(selector).type(text, delay=delay)

def clear(page: Page, selector: str):
    """清空输入框"""
    page.locator(selector).clear() 