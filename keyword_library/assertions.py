import functools
import re
from typing import List

import allure
from playwright.sync_api import Page, expect
from pytest_check import check

from utils.logger import logger


def check_and_screenshot(description="Assertion"):
    """
    装饰器，用于捕获断言失败并进行截图。
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            step_description = description
            # 从 self (Assertion 实例) 中获取 page 对象
            page = getattr(self, "page", None)
            if not isinstance(page, Page):
                raise TypeError("Assertion methods must be called on an instance with a 'page' attribute.")

            selector = kwargs.get("selector", None)
            expected = kwargs.get("expected", None)
            if selector:
                step_description += f"，断言元素: {selector}"
            if expected:
                step_description += f"，断言值: {expected}"

            try:
                with allure.step(f"{step_description}"):
                    try:
                        # 执行断言，并将 page 作为第一个参数传递
                        return func(self, page, *args, **kwargs)
                    except AssertionError as e:
                        simplified_error = f"断言失败：{step_description}"
                        error_str = str(e)
                        actual_pattern = r"Actual value: ([^\n]*)"
                        actual_match = re.search(actual_pattern, error_str)
                        actual_text = actual_match.group(1) if actual_match else ""
                        if actual_text:
                            simplified_error += f"，实际值: '{actual_text}'"
                        
                        screenshot = page.screenshot()
                        allure.attach(
                            screenshot,
                            name="失败截图",
                            attachment_type=allure.attachment_type.PNG,
                        )
                        raise AssertionError(simplified_error)
            except Exception as e:
                check.fail(e)
                if not hasattr(e, "_logged"):
                    logger.error(e)
                    setattr(e, "_logged", True)
                raise AssertionError(e)

        return wrapper

    return decorator


class Assertions:
    def __init__(self, page: Page):
        self.page = page

    @check_and_screenshot("断言URL")
    def assert_url(self, page: Page, expected: str):
        expect(page).to_have_url(expected)

    @check_and_screenshot("断言文本")
    def assert_text(self, page: Page, selector: str, expected: str):
        expect(page.locator(selector)).to_have_text(expected)
        
    @allure.step("硬断言元素文本")
    def hard_assert_text(self, page: Page, selector: str, expected: str):
        expect(page.locator(selector)).to_have_text(expected)

    @check_and_screenshot("断言页面标题")
    def assert_title(self, page: Page, expected: str):
        expect(page).to_have_title(expected)

    @check_and_screenshot("断言元素数量")
    def assert_element_count(self, page: Page, selector: str, expected: int):
        expect(page.locator(selector)).to_have_count(expected)

    @check_and_screenshot("断言元素包含文本")
    def assert_text_contains(self, page: Page, selector: str, expected: str):
        expect(page.locator(selector)).to_contain_text(expected)

    @check_and_screenshot("断言URL包含")
    def assert_url_contains(self, page: Page, expected: str):
        expect(page).to_have_url(re.compile(f".*{expected}.*"))

    @check_and_screenshot("断言元素存在")
    def assert_exists(self, page: Page, selector: str):
        expect(page.locator(selector).first).to_be_visible()

    @check_and_screenshot("断言元素不存在")
    def assert_not_exists(self, page: Page, selector: str):
        expect(page.locator(selector).first).not_to_be_visible()

    @check_and_screenshot("断言元素启用状态")
    def assert_element_enabled(self, page: Page, selector: str):
        expect(page.locator(selector)).to_be_enabled()

    @check_and_screenshot("断言元素禁用状态")
    def assert_element_disabled(self, page: Page, selector: str):
        expect(page.locator(selector)).to_be_disabled()

    @check_and_screenshot("断言元素可见性")
    def assert_visible(self, page: Page, selector: str):
        expect(page.locator(selector).first).to_be_visible()

    @check_and_screenshot("断言元素不可见")
    def assert_not_visible(self, page: Page, selector: str):
        expect(page.locator(selector).first).not_to_be_visible()
        
    @check_and_screenshot("断言元素隐藏")
    def assert_be_hidden(self, page: Page, selector: str):
        expect(page.locator(selector)).to_be_hidden()

    @check_and_screenshot("断言元素属性值")
    def assert_attribute(self, page: Page, selector: str, attribute: str, expected: str):
        expect(page.locator(selector)).to_have_attribute(attribute, expected)

    @check_and_screenshot("断言元素值")
    def assert_value(self, page: Page, selector: str, expected: str):
        expect(page.locator(selector)).to_have_value(expected)

    @check_and_screenshot("断言元素已选中")
    def assert_checked(self, page: Page, selector: str):
        expect(page.locator(selector)).to_be_checked()
        
    @check_and_screenshot("断言元素值列表")
    def assert_values(self, page: Page, selector: str, expected: List[str]):
        expect(page.locator(selector)).to_have_values(expected)

    @check_and_screenshot("断言元素有精确文本")
    def assert_exact_text(self, page: Page, selector: str, expected: str):
        expect(page.locator(selector)).to_have_text(expected, use_inner_text=True)

    @check_and_screenshot("断言元素匹配文本正则")
    def assert_text_matches(self, page: Page, selector: str, pattern: str):
        expect(page.locator(selector)).to_have_text(re.compile(pattern)) 