from typing import Literal
from playwright.sync_api import Page, Request
from constants import DEFAULT_TIMEOUT

def wait_for_element(page: Page, selector: str, state: Literal["attached", "detached", "hidden", "visible"] = "visible", timeout: int = DEFAULT_TIMEOUT):
    """等待元素达到指定状态"""
    page.wait_for_selector(selector, state=state, timeout=timeout)

def wait_for_element_to_be_visible(page: Page, selector: str, timeout: int = DEFAULT_TIMEOUT):
    """等待元素可见"""
    page.wait_for_selector(selector, state="visible", timeout=timeout)

def wait_for_element_to_be_hidden(page: Page, selector: str, timeout: int = DEFAULT_TIMEOUT):
    """等待元素不可见（隐藏）"""
    page.wait_for_selector(selector, state="hidden", timeout=timeout)

def wait_for_element_to_be_attached(page: Page, selector: str, timeout: int = DEFAULT_TIMEOUT):
    """等待元素附加到DOM"""
    page.wait_for_selector(selector, state="attached", timeout=timeout)

def wait_for_element_to_be_detached(page: Page, selector: str, timeout: int = DEFAULT_TIMEOUT):
    """等待元素从DOM中分离"""
    page.wait_for_selector(selector, state="detached", timeout=timeout)

def wait_for_timeout(page: Page, timeout: int):
    """等待指定毫秒数"""
    page.wait_for_timeout(timeout)

def wait_for_load_state(page: Page, state: Literal["load", "domcontentloaded", "networkidle"] = "load"):
    """等待页面加载状态"""
    page.wait_for_load_state(state, timeout=DEFAULT_TIMEOUT)

def wait_for_navigation(page: Page, url: str = None, timeout: int = DEFAULT_TIMEOUT):
    """等待页面导航完成"""
    page.wait_for_url(url, timeout=timeout)

def wait_for_request(page: Page, url_or_predicate, timeout: int = DEFAULT_TIMEOUT) -> Request:
    """等待网络请求"""
    return page.wait_for_request(url_or_predicate, timeout=timeout)

def wait_for_response(page: Page, url_or_predicate, timeout: int = DEFAULT_TIMEOUT) -> Request:
    """等待网络响应"""
    return page.wait_for_response(url_or_predicate, timeout=timeout) 