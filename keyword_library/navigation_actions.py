from typing import List, Any
from playwright.sync_api import Page
from utils.logger import logger

def navigate(page: Page, url: str):
    """导航到指定URL"""
    page.goto(url)
    # page.wait_for_load_state()

def refresh(page: Page):
    """刷新当前页面"""
    page.reload()
    page.wait_for_load_state("networkidle")

def go_back(page: Page):
    """后退到上一页"""
    page.go_back()

def go_forward(page: Page):
    """前进到下一页"""
    page.go_forward()

def switch_to_window_by_index(page: Page, pages: List[Page], index: int) -> Page:
    """按索引切换到指定窗口/标签页"""
    if 0 <= index < len(pages):
        new_page = pages[index]
        new_page.bring_to_front()
        logger.info(f"切换到页面索引 {index}")
        return new_page
    else:
        raise IndexError(f"页面索引 {index} 超出范围 (共 {len(pages)} 个页面)")

def handle_new_tab(page: Page, selector: str, action: str = "click") -> Page:
    """
    通过指定操作（如点击）打开新标签页，并返回新的页面对象。
    :param page: 当前页面对象。
    :param selector: 触发新标签页的元素选择器。
    :param action: 触发动作，默认为'click'。
    :return: 新的页面对象。
    """
    with page.expect_popup() as popup_info:
        if action == "click":
            page.locator(selector).click()
    new_page = popup_info.value
    logger.info(f"已打开新标签页: {new_page.url}")
    return new_page

def enter_frame(page: Page, selector: str):
    """进入iframe"""
    # Playwright 的 FrameLocator 本身就代表了进入了 frame，可以直接链式操作
    # 这个函数主要是为了关键字映射，实际返回的是 FrameLocator
    return page.frame_locator(selector)

def leave_frame():
    """离开iframe，返回主文档"""
    # 在 Playwright 中，你不需要显式离开 frame。
    # 只需要在下一次操作时，直接使用 page 对象而不是 frame_locator 对象即可。
    # 这个函数留作关键字，但在Python代码层面是无操作的（no-op）。
    logger.debug("逻辑上离开 iframe，后续操作将针对主页面。")
    pass 