from typing import List, Any
from playwright.sync_api import Page

def get_text(page: Page, selector: str) -> str:
    """获取元素的内部文本。"""
    return page.locator(selector).first.inner_text()

def get_value(page: Page, selector:str) -> str:
    """获取输入元素的 value 属性。"""
    return page.locator(selector).first.input_value()

def get_attribute(page: Page, selector: str, attribute: str) -> str:
    """获取元素的任意属性。"""
    return page.locator(selector).first.get_attribute(attribute)

def get_element_count(page: Page, selector: str) -> int:
    """获取匹配选择器的元素数量。"""
    return page.locator(selector).count()

def get_current_url(page: Page) -> str:
    """获取当前页面的URL。"""
    return page.url

def get_page_title(page: Page) -> str:
    """获取当前页面的标题。"""
    return page.title()

def get_all_elements(page: Page, selector: str) -> List[Any]:
    """获取所有匹配选择器的元素句柄列表。"""
    return page.locator(selector).all() 