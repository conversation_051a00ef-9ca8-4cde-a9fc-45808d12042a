#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def test_decode_logic():
    """测试Unicode解码逻辑"""
    # 模拟从日志中看到的乱码
    garbled_texts = [
        "æ£æ¥ç­çº§",  # 检查等级
        "é¨åºä¿¡æ¯",  # 门店信息
        "æçè¯´æ",    # 权益说明
        "é¨åºåè¡¨"   # 门店列表
    ]
    
    for garbled in garbled_texts:
        print(f"乱码文本: {garbled}")
        
        try:
            # 尝试将乱码重新编码为UTF-8
            decoded = garbled.encode("latin-1").decode("utf-8")
            print(f"解码结果: {decoded}")
        except Exception as e:
            print(f"解码失败: {e}")
        
        print("-" * 40)

if __name__ == "__main__":
    test_decode_logic()
