"""
测试断言统计功能
"""

import pytest
from src.assertion_manager import assertion_manager


def test_assertion_manager_basic():
    """测试断言管理器基本功能"""
    # 重置统计
    assertion_manager.reset_stats()
    assertion_manager.set_current_test_case("测试断言管理器")
    
    # 测试软断言
    result1 = assertion_manager.soft_assert(True, "软断言成功", "期望值", "实际值", "测试步骤1")
    assert result1 == True
    
    result2 = assertion_manager.soft_assert(False, "软断言失败", "期望值", "错误值", "测试步骤2")
    assert result2 == False
    
    # 测试硬断言成功
    assertion_manager.hard_assert(True, "硬断言成功", "期望值", "实际值", "测试步骤3")
    
    # 测试硬断言失败（应该抛出异常）
    with pytest.raises(AssertionError):
        assertion_manager.hard_assert(False, "硬断言失败", "期望值", "错误值", "测试步骤4")
    
    # 检查统计信息
    stats = assertion_manager.get_stats()
    assert stats.total_assertions == 4
    assert stats.passed_assertions == 2
    assert stats.failed_soft_assertions == 1
    assert stats.failed_hard_assertions == 1
    assert stats.success_rate == 50.0
    
    # 检查失败断言
    failed_assertions = assertion_manager.get_failed_assertions()
    assert len(failed_assertions) == 2
    
    # 检查软断言失败
    soft_failed = assertion_manager.get_failed_assertions("soft")
    assert len(soft_failed) == 1
    assert soft_failed[0].assertion_type == "soft"
    
    # 检查硬断言失败
    hard_failed = assertion_manager.get_failed_assertions("hard")
    assert len(hard_failed) == 1
    assert hard_failed[0].assertion_type == "hard"
    
    print("✅ 断言管理器功能测试通过")


if __name__ == "__main__":
    test_assertion_manager_basic()
