[tool.poetry]
name = "playwright-ui"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.10"
playwright = "^1.49.1"
pytest = "^8.3.4"
allure-python-commons = "^2.13.5"
pyyaml = "^6.0.2"
rich = "^13.9.4"
allure-pytest = "^2.13.5"
loguru = "^0.7.3"
pandas = "^2.2.3"
openpyxl = "^3.1.5"
typer = "^0.15.1"
pydantic = "^2.10.4"
pydantic-settings = "^2.7.1"
requests = "^2.32.3"
pytest-reportlog = "^0.4.0"
ruamel-yaml = "^0.18.10"
configobj = "^5.0.9"
pytest-base-url = "^2.1.0"
pytest-dependency = "^0.6.0"
poetry-core = "^2.0.1"
faker = "^37.0.0"
pytest-playwright = "^0.7.0"
pytest-check = "^2.5.0"
jsonpath-ng = "^1.7.0"
psutil = "^7.0.0"

[tool.poetry.group.dev.dependencies]
black = "^25.1.0"
pytest-cov = "^4.0.0"
mypy = "^1.0.0"
pre-commit = "^3.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

