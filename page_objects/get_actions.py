from typing import List, Any
from playwright.sync_api import Page

def get_text(page: Page, selector: str) -> str:
    """获取元素文本"""
    return page.locator(selector).first.inner_text()

def get_value(page: Page, selector: str) -> str:
    """获取输入框的值"""
    return page.locator(selector).input_value()

def get_element_attribute(page: Page, selector: str, attribute: str) -> str:
    """获取元素属性"""
    return page.locator(selector).get_attribute(attribute)

def get_element_count(page: Page, selector: str) -> int:
    """获取元素数量"""
    return page.locator(selector).count()
    
def get_all_elements(page: Page, selector: str) -> List[Any]:
    """获取所有匹配的元素句柄"""
    return page.locator(selector).element_handles()

def get_current_url(page: Page) -> str:
    """获取当前URL"""
    return page.url

def get_page_title(page: Page) -> str:
    """获取页面标题"""
    return page.title() 