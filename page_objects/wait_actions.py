from playwright.sync_api import Page
from constants import DEFAULT_TIMEOUT

def wait_for_seconds(page: Page, seconds: int):
    """强制等待指定的秒数"""
    page.wait_for_timeout(seconds * 1000)

def wait_for_element_visible(page: Page, selector: str, timeout: int = DEFAULT_TIMEOUT):
    """等待元素可见"""
    page.locator(selector).wait_for(state="visible", timeout=timeout)

def wait_for_element_hidden(page: Page, selector: str, timeout: int = DEFAULT_TIMEOUT):
    """等待元素不可见"""
    page.locator(selector).wait_for(state="hidden", timeout=timeout)

def wait_for_element_enabled(page: Page, selector: str, timeout: int = DEFAULT_TIMEOUT):
    """等待元素可用"""
    page.locator(selector).wait_for(state="enabled", timeout=timeout)

def wait_for_element_disabled(page: Page, selector: str, timeout: int = DEFAULT_TIMEOUT):
    """等待元素不可用"""
    page.locator(selector).wait_for(state="disabled", timeout=timeout)

def wait_for_element_text(page: Page, selector: str, text: str, timeout: int = DEFAULT_TIMEOUT):
    """等待元素文本包含特定内容"""
    # Playwright's default locator assertions handle this implicitly.
    # To make it an explicit wait action, we can use `expect`.
    from playwright.sync_api import expect
    expect(page.locator(selector)).to_contain_text(text, timeout=timeout)

def wait_for_navigation(page: Page, url: str = None, timeout: int = DEFAULT_TIMEOUT):
    """等待页面导航完成"""
    page.wait_for_url(f"**/{url}/**" if url else None, timeout=timeout)
    page.wait_for_load_state() 