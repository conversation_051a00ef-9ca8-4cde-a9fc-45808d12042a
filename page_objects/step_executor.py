"""
统一的步骤执行器，支持所有操作类型
"""
from typing import Dict, Any, List
from playwright.sync_api import Page
from page_objects.base_page import BasePage


class StepExecutor:
    """统一的步骤执行器"""
    
    def __init__(self, page: Page, elements: Dict[str, Any] = None):
        self.page = page
        self.ui_helper = BasePage(page)
        self.elements = elements or {}
        self.has_error = False
        self.step_has_error = False
        
        # 支持的操作类型
        self.supported_actions = {
            # 基础操作
            'navigate', 'goto', '打开', '访问',
            'click', '点击',
            'fill', '输入',
            'press_key', '按键',
            'wait', '等待',
            
            # 断言操作
            'assert_text', 'assertion', '验证文本', '验证', 'verify',
            'hard_assert', '硬断言',
            'assert_visible', '验证可见',
            'assert_be_hidden', '验证隐藏',
            'assert_attribute', '验证属性',
            'assert_url', '验证URL',
            'assert_title', '验证标题',
            'assert_element_count', '验证元素数量',
            'assert_text_contains', '验证包含文本',
            'assert_url_contains', '验证URL包含',
            'assert_exists', '验证存在',
            'assert_not_exists', '验证不存在',
            'assert_enabled', '验证启用',
            'assert_disabled', '验证禁用',
            'assert_text_matches', '验证文本匹配',
            
            # 其他操作
            'refresh', '刷新',
            'pause', '暂停',
            'upload', '上传', '上传文件',
            'hover', '悬停',
            'double_click', '双击',
            'right_click', '右键点击',
            'select', '选择',
            'drag_and_drop', '拖拽',
            'get_value', '获取值',
            'scroll_into_view', '滚动到元素',
            'scroll_to', '滚动到位置',
            'focus', '聚焦',
            'blur', '失焦',
            'type', '模拟输入',
            'clear', '清空',
            'store_text', '存储文本',
            'monitor_request', '监测请求',
            'monitor_response', '监测响应',
        }
        
        # 不需要selector的操作
        self.no_selector_actions = {
            'navigate', 'goto', '打开', '访问',
            'assert_url', '验证URL',
            'assert_title', '验证标题',
            'assert_url_contains', '验证URL包含',
            'wait', '等待',
            'refresh', '刷新',
            'pause', '暂停',
        }

    def execute_step(self, step: Dict[str, Any]) -> None:
        """执行单个步骤"""
        try:
            self.step_has_error = False
            
            action = step.get("action", "").lower()
            pre_selector = step.get("selector")
            selector = self.elements.get(pre_selector, pre_selector) if pre_selector else None
            value = step.get("value")
            
            # 验证步骤
            self._validate_step(action, selector)
            
            # 执行操作
            self._execute_action(action, selector, value, step)
            
        except Exception as e:
            self.has_error = True
            self.step_has_error = True
            print(f"步骤执行失败: {e}")
            raise

    def execute_steps(self, steps: List[Dict[str, Any]]) -> None:
        """执行多个步骤"""
        for step in steps:
            self.execute_step(step)

    def _validate_step(self, action: str, selector: str) -> None:
        """验证步骤参数"""
        if not action:
            raise ValueError("步骤缺少必要参数: action")
        
        if action not in self.supported_actions:
            raise ValueError(f"不支持的操作类型: {action}")
        
        if action not in self.no_selector_actions and not selector:
            raise ValueError(f"操作 {action} 需要提供selector参数")

    def _execute_action(self, action: str, selector: str, value: Any = None, step: Dict[str, Any] = None) -> None:
        """执行具体操作"""
        try:
            # 导航操作
            if action in ['navigate', 'goto', '打开', '访问']:
                self.ui_helper.navigate(value)
            
            # 基础UI操作
            elif action in ['click', '点击']:
                self.ui_helper.click(selector)
            elif action in ['fill', '输入']:
                self.ui_helper.fill(selector, value)
            elif action in ['press_key', '按键']:
                self.ui_helper.press_key(selector, value)
            elif action in ['hover', '悬停']:
                self.ui_helper.hover(selector)
            elif action in ['double_click', '双击']:
                self.ui_helper.double_click(selector)
            elif action in ['right_click', '右键点击']:
                self.ui_helper.right_click(selector)
            elif action in ['select', '选择']:
                self.ui_helper.select_option(selector, value)
            elif action in ['upload', '上传', '上传文件']:
                self.ui_helper.upload_file(selector, value)
            
            # 断言操作
            elif action in ['assert_text', 'assertion', '验证文本', '验证', 'verify']:
                expected = step.get("expected", value)
                self.ui_helper.assertion.assert_text(self.page, selector, expected)
            elif action in ['hard_assert', '硬断言']:
                self.ui_helper.assertion.hard_assert_text(self.page, selector, value)
            elif action in ['assert_visible', '验证可见']:
                self.ui_helper.assertion.assert_visible(self.page, selector)
            elif action in ['assert_be_hidden', '验证隐藏']:
                self.ui_helper.assertion.assert_be_hidden(self.page, selector)
            elif action in ['assert_url', '验证URL']:
                self.ui_helper.assertion.assert_url(self.page, value)
            elif action in ['assert_title', '验证标题']:
                self.ui_helper.assertion.assert_title(self.page, value)
            elif action in ['assert_element_count', '验证元素数量']:
                expected = step.get("expected", value)
                self.ui_helper.assertion.assert_element_count(self.page, selector, int(expected))
            elif action in ['assert_text_contains', '验证包含文本']:
                expected = step.get("expected", value)
                self.ui_helper.assertion.assert_text_contains(self.page, selector, expected)
            elif action in ['assert_text_matches', '验证文本匹配']:
                self.ui_helper.assertion.assert_text_matches(self.page, selector, value)
            
            # 等待操作
            elif action in ['wait', '等待']:
                if isinstance(value, (int, float)):
                    self.ui_helper.wait_for_timeout(int(value * 1000))  # 转换为毫秒
                else:
                    self.ui_helper.wait_for_timeout(int(value))
            
            # 其他操作
            elif action in ['refresh', '刷新']:
                self.ui_helper.refresh()
            elif action in ['pause', '暂停']:
                self.ui_helper.pause()
            elif action in ['store_text', '存储文本']:
                variable_name = step.get("variable_name")
                scope = step.get("scope", "global")
                if not variable_name:
                    raise ValueError("store_text 操作需要提供 variable_name 参数")
                self.ui_helper.store_text(selector, variable_name, scope)
            
            # 网络监测操作
            elif action in ['monitor_request', '监测请求']:
                url_pattern = step.get("url_pattern")
                action_type = step.get("action_type", "click")
                assert_params = step.get("assert_params")
                timeout = step.get("timeout")
                if not url_pattern:
                    raise ValueError("monitor_request 操作需要提供 url_pattern 参数")
                self.ui_helper.monitor_action_request(
                    url_pattern=url_pattern,
                    selector=selector,
                    action=action_type,
                    assert_params=assert_params,
                    timeout=timeout,
                    value=value
                )
            elif action in ['monitor_response', '监测响应']:
                url_pattern = step.get("url_pattern")
                action_type = step.get("action_type", "click")
                assert_params = step.get("assert_params")
                save_params = step.get("save_params")
                timeout = step.get("timeout")
                if not url_pattern:
                    raise ValueError("monitor_response 操作需要提供 url_pattern 参数")
                self.ui_helper.monitor_action_response(
                    url_pattern=url_pattern,
                    selector=selector,
                    action=action_type,
                    assert_params=assert_params,
                    save_params=save_params,
                    timeout=timeout,
                    value=value
                )
            
            else:
                raise ValueError(f"不支持的操作类型: {action}")
                
        except Exception as e:
            self.step_has_error = True
            self.has_error = True
            raise
