from typing import Any
from playwright.sync_api import Page
from constants import DEFAULT_TYPE_DELAY, DEFAULT_TIMEOUT

# 注意：为了保持简洁，这里暂时移除了原有的 @handle_page_error 装饰器。
# 在一个完整的重构中，我们应该创建一个通用的装饰器，并应用于这些函数。

def click(page: Page, selector: str):
    """点击元素"""
    page.locator(selector).first.click(timeout=DEFAULT_TIMEOUT)

def fill(page: Page, selector: str, value: Any):
    """在输入框中填写文本。注意：变量替换的逻辑需要在这里或调用前处理。"""
    # 假设 value 已经是解析后的最终字符串
    page.locator(selector).fill(str(value))

def press_key(page: Page, selector: str, key: str):
    """在元素上按键"""
    page.locator(selector).press(key)

def upload_file(page: Page, selector: str, file_path: str):
    """上传文件"""
    page.set_input_files(selector, file_path)

def hover(page: Page, selector: str):
    """鼠标悬停"""
    page.locator(selector).hover()

def double_click(page: Page, selector: str):
    """双击元素"""
    page.locator(selector).dblclick()

def right_click(page: Page, selector: str):
    """右键点击元素"""
    page.locator(selector).click(button="right")

def select_option(page: Page, selector: str, value: str):
    """通过值选择下拉选项"""
    page.locator(selector).select_option(value)

def drag_and_drop(page: Page, source_selector: str, target_selector: str):
    """拖放元素"""
    page.locator(source_selector).drag_to(page.locator(target_selector))

def scroll_into_view(page: Page, selector: str):
    """滚动到元素可见"""
    page.locator(selector).scroll_into_view_if_needed()

def focus(page: Page, selector: str):
    """聚焦元素"""
    page.locator(selector).focus()

def blur(page: Page, selector: str):
    """使元素失焦"""
    # Playwright's blur is on the page, not locator. We can use JS.
    page.evaluate("(element) => element.blur()", page.locator(selector).element_handle())

def type_text(page: Page, selector:str, text: str, delay: int = DEFAULT_TYPE_DELAY):
    """模拟键盘输入"""
    page.locator(selector).type(text, delay=delay)

def clear(page: Page, selector: str):
    """清空输入框"""
    page.locator(selector).clear() 