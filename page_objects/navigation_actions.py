from typing import List
from playwright.sync_api import Page, Dialog

def navigate(page: Page, url: str):
    """导航到指定URL"""
    page.goto(url)
    page.wait_for_load_state()

def refresh(page: Page):
    """刷新页面"""
    page.reload()

def accept_alert(page: Page, trigger_selector: str, prompt_text: str = None):
    """处理弹窗 - 接受"""
    def handle_dialog(dialog: Dialog):
        if prompt_text:
            dialog.accept(prompt_text)
        else:
            dialog.accept()
    page.once("dialog", handle_dialog)
    page.locator(trigger_selector).click()

def dismiss_alert(page: Page, trigger_selector: str):
    """处理弹窗 - 拒绝"""
    page.once("dialog", lambda dialog: dialog.dismiss())
    page.locator(trigger_selector).click()

def switch_to_window(page: Page, pages: List[Page], identifier: any) -> Page:
    """切换到指定窗口"""
    new_page = page
    if isinstance(identifier, int):
        if identifier < len(pages):
            new_page = pages[identifier]
    else:
        for p in pages:
            if p.title() == identifier or p.url == identifier:
                new_page = p
                break
    new_page.bring_to_front()
    return new_page

def close_window(page: Page, pages: List[Page]) -> (Page, List[Page]):
    """关闭当前窗口并返回新的当前窗口和页面列表"""
    if len(pages) > 1:
        current_page_index = pages.index(page)
        page.close()
        pages.pop(current_page_index)
        new_page = pages[-1] # 默认切换到最后一个
        new_page.bring_to_front()
        return new_page, pages
    return page, pages

def enter_frame(page: Page, selector: str) -> Page:
    """切换到iframe"""
    frame_locator = page.frame_locator(selector)
    # Playwright's FrameLocator doesn't directly expose a 'page' object
    # but actions can be performed on the locator itself.
    # For a more compatible API, we might need a wrapper.
    # For now, we assume the user will work with the frame locator's context.
    # This part of the API might need more advanced refactoring.
    # Returning the main page for now.
    return frame_locator.page

def expect_popup(page: Page, trigger_action: callable) -> Page:
    """期望一个动作触发弹出新页面"""
    with page.expect_popup() as popup_info:
        trigger_action()
    new_page = popup_info.value
    new_page.wait_for_load_state()
    return new_page 