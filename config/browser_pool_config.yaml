# 浏览器池优化配置

# 基础配置
basic:
  max_browsers: 3              # 最大浏览器实例数
  max_idle_time: 300          # 最大空闲时间（秒）
  cleanup_interval: 60        # 清理检查间隔（秒）
  max_tests_per_instance: 50  # 单个实例最大测试数量

# 关闭优化配置
shutdown:
  fast_shutdown: true         # 启用快速关闭（并行处理）
  max_shutdown_time: 5.0     # 最大关闭时间（秒）
  per_instance_timeout: 2.0  # 单个实例关闭超时（秒）
  max_parallel_workers: 5    # 最大并行关闭线程数
  cleanup_thread_timeout: 2  # 清理线程停止超时（秒）

# 健康检查配置
health_check:
  enable_auto_cleanup: true   # 启用自动清理不健康实例
  check_interval: 120        # 健康检查间隔（秒）
  unhealthy_threshold: 3     # 不健康实例阈值

# 性能优化配置
performance:
  enable_instance_reuse: true    # 启用实例复用
  reuse_threshold: 0.8          # 复用率阈值
  memory_limit_mb: 500          # 单个实例内存限制（MB）
  enable_lazy_cleanup: true     # 启用延迟清理

# 错误处理配置
error_handling:
  max_retry_attempts: 3       # 最大重试次数
  retry_delay: 1.0           # 重试延迟（秒）
  enable_fallback_mode: true # 启用降级模式
  
# 日志配置
logging:
  log_level: "INFO"          # 日志级别
  enable_detailed_stats: true # 启用详细统计
  log_instance_lifecycle: false # 记录实例生命周期
