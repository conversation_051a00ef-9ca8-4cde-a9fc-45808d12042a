projects:
  demo:
    test_dir: "test_data/demo"
    environments:
      dev: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
      test: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
      stage: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
      prod: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
    browser_config: { "viewport": { "width": 1080, "height": 798 } }

  hololive:
    test_dir: "test_data/hololive"
    environments:
      dev: "http://dev.demo.com"
      test: "http://test.demo.com"
      stage: "http://stage.demo.com"
      prod: "http://prod.demo.com"
    browser_config: { 'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.2 Mobile/15E148 Safari/604.1', 'viewport': { 'width': 393, 'height': 659 }, 'device_scale_factor': 3, 'is_mobile': True, 'has_touch': True, 'default_browser_type': 'webkit' }

  assistant:
    test_dir: "test_data/assistant"
    environments:
      dev: "http://dev.hololive.com"
      test: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
      stage: "http://stage.hololive.com"
      prod: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
    browser_config: { "viewport": { "width": 1920, "height": 1080 } }

  ahoh:
    test_dir: "test_data/ahoh"
    environments:
      dev: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
      test: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
      stage: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
      prod: "http://app.ahohcrm.autohome.com.cn/v/login"
      #店端后台 online https://ahohcrm.autohome.com.cn/#/login
      # online-old:https://ahohcrm.app.autohome.com.cn/v/index#online：http://app.ahohcrm.autohome.com.cn/v/login #Test：http://test.crmapp.ahoh.mulan.corpautohome.com/v/index
      #线上压测环境：http://ahohcrm-pressure.app.thallo.corpautohome.com/v/
    browser_config: { "viewport": { "width": 393, "height": 989 }, "device_scale_factor": 3, "is_mobile": True, "has_touch": True, "default_browser_type": "webkit" }

  crm_backend:
    test_dir: "test_data/crm_backend"
    environments:
      dev: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
      test: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
      stage: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
      prod: "http://edit.ahohcrm.corpautohome.com/"
    browser_config: { "viewport": { "width": 1600, "height": 900 } }

  marketing:
    test_dir: "test_data/marketing"
    environments:
      dev: "http://dev.demo.com"
      test: "http://test.demo.com"
      stage: "http://stage.demo.com"
      prod: "https://energyspace.m.autohome.com.cn"
    browser_config: { 'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.2 Mobile/15E148 Safari/604.1', 'viewport': { 'width': 393, 'height': 659 }, 'device_scale_factor': 3, 'is_mobile': True, 'has_touch': True, 'default_browser_type': 'webkit' }

  channel_page:
    test_dir: "test_data/channel_page"
    environments:
      dev: "http://dev.demo.com"
      test: "http://test.demo.com"
      stage: "http://stage.demo.com"
      prod: "http://prod.demo.com"
    browser_config: { 'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.2 Mobile/15E148 Safari/604.1', 'viewport': { 'width': 393, 'height': 659 }, 'device_scale_factor': 3, 'is_mobile': True, 'has_touch': True, 'default_browser_type': 'webkit' }

  crm_store_backend:
    test_dir: "test_data/crm_store_backend"
    environments:
      dev: "http://dev.hololive.com"
      test: "https://energyspace-c-test.autohome.com.cn/live-assistant/assistant"
      stage: "http://stage.hololive.com"
      prod: "https://ahohcrm.autohome.com.cn/#/login"
    browser_config: { "viewport": { "width": 1920, "height": 1080 } }


  other_project:
    test_dir: "test_data/other_project"
    environments:
      dev: "http://dev.other.com"
      test: "http://test.other.com"
      stage: "http://stage.other.com"
      prod: "http://prod.other.com"
  da_ping:
    test_dir: "test_data/da_ping"
    environments:
      dev: "http://devapp.terra.corpautohome.com/screen/home?storeId=94084"
      test: "https://ahohcrm-test.autohome.com.cn/screen/home?storeId=94084"
      stage: "https://ahohcrm-test.autohome.com.cn/screen/home?storeId=94084"
      prod: "https://ahohcrm-pre.autohome.com.cn/screen/home?storeId=3425"
    browser_config: { "viewport": { "width": 1200, "height": 600 } }

