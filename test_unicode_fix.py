#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Unicode解码修复
"""

def test_unicode_decode():
    """测试Unicode解码功能"""
    test_cases = [
        "test_store_info[门店信息]",
        "test_check_level[检查等级]", 
        "test_rights_detail[权益说明]",
        "test_store_list[门店列表]"
    ]
    
    for test_name in test_cases:
        try:
            # 模拟pytest中的Unicode转义序列
            escaped_name = test_name.encode("unicode_escape").decode("utf-8")
            print(f"原始名称: {test_name}")
            print(f"转义后: {escaped_name}")
            
            # 测试解码逻辑
            decoded_name = escaped_name.encode("utf-8").decode("unicode_escape")
            if "\\u" in decoded_name:
                decoded_name = decoded_name.encode().decode("unicode_escape")
            
            print(f"解码后: {decoded_name}")
            print("-" * 50)
            
        except Exception as e:
            print(f"解码失败: {test_name} - {e}")

if __name__ == "__main__":
    test_unicode_decode()
