"""
UI 操作相关的命令
"""
from typing import Dict, Any

from keyword_library import ui_actions
from src.step_actions.action_types import StepAction
from src.step_actions.commands.base_command import Command, CommandFactory

@CommandFactory.register(StepAction.CLICK)
class ClickCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        ui_actions.click(ui_helper.page, selector)

@CommandFactory.register(StepAction.FILL)
class FillCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        ui_actions.fill(ui_helper.page, selector, value)

@CommandFactory.register(StepAction.PRESS_KEY)
class PressKeyCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        ui_actions.press_key(ui_helper.page, selector, value)

@CommandFactory.register(StepAction.UPLOAD)
class UploadFileCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        ui_actions.upload_file(ui_helper.page, selector, value)

@CommandFactory.register(StepAction.HOVER)
class HoverCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        ui_actions.hover(ui_helper.page, selector)

@CommandFactory.register(StepAction.DOUBLE_CLICK)
class DoubleClickCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        ui_actions.double_click(ui_helper.page, selector)

@CommandFactory.register(StepAction.RIGHT_CLICK)
class RightClickCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        ui_actions.right_click(ui_helper.page, selector)

@CommandFactory.register(StepAction.SELECT)
class SelectOptionCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        ui_actions.select_option(ui_helper.page, selector, value)

@CommandFactory.register(StepAction.DRAG_AND_DROP)
class DragAndDropCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        target_selector = step.get("target_selector")
        if not target_selector:
            raise ValueError("Drag and drop action requires a 'target_selector'")
        ui_actions.drag_and_drop(ui_helper.page, selector, target_selector)

@CommandFactory.register(StepAction.FOCUS)
class FocusCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        ui_actions.focus(ui_helper.page, selector)

@CommandFactory.register(StepAction.BLUR)
class BlurCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        ui_actions.blur(ui_helper.page, selector)

@CommandFactory.register(StepAction.TYPE)
class TypeTextCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        delay = step.get("delay")
        if delay:
            ui_actions.type_text(ui_helper.page, selector, value, delay=int(delay))
        else:
            ui_actions.type_text(ui_helper.page, selector, value)
            
@CommandFactory.register(StepAction.CLEAR)
class ClearCommand(Command):
    def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> None:
        ui_actions.clear(ui_helper.page, selector) 