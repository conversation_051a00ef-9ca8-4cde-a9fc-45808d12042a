# """
# 获取信息相关的命令
# """
# from typing import Dict, Any
#
# from keyword_library import get_actions
# from src.step_actions.action_types import StepAction
# from src.step_actions.commands.base_command import Command, CommandFactory
#
# @CommandFactory.register(StepAction.GET_TEXT)
# class GetTextCommand(Command):
#     def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> str:
#         return get_actions.get_text(ui_helper.page, selector)
#
# @CommandFactory.register(StepAction.GET_VALUE)
# class GetValueCommand(Command):
#     def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> str:
#         return get_actions.get_value(ui_helper.page, selector)
#
# @CommandFactory.register(StepAction.GET_ATTRIBUTE)
# class GetAttributeCommand(Command):
#     def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> str:
#         attribute_name = value
#         if not attribute_name:
#             raise ValueError("Get attribute action requires the attribute name in 'value'")
#         return get_actions.get_attribute(ui_helper.page, selector, attribute_name)
#
# @CommandFactory.register(StepAction.GET_ELEMENT_COUNT)
# class GetElementCountCommand(Command):
#     def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> int:
#         return get_actions.get_element_count(ui_helper.page, selector)
#
# @CommandFactory.register(StepAction.GET_CURRENT_URL)
# class GetCurrentUrlCommand(Command):
#     def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> str:
#         return get_actions.get_current_url(ui_helper.page)
#
# @CommandFactory.register(StepAction.GET_PAGE_TITLE)
# class GetPageTitleCommand(Command):
#     def execute(self, ui_helper, selector: str, value: Any, step: Dict[str, Any]) -> str:
#         return get_actions.get_page_title(ui_helper.page)