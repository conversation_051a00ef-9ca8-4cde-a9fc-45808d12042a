from typing import Dict, Any, Set

import allure

# 导入重构后的StepExecutor
from src.step_actions.step_executor import StepExecutor
from src.assertion_manager import assertion_manager
from utils.logger import logger

log = logger


def _cleanup_test_environment(case: Dict[str, Any]) -> None:
    with allure.step("测试环境清理"):
        log.debug(f"Cleaning up test environment for case: {case['name']}")
        # fixture 的清理会由 pytest 自动处理


def _setup_test_environment(case: Dict[str, Any]) -> None:
    with allure.step("测试环境准备"):
        log.debug(f"Setting up test environment for case: {case['name']}")
        # 添加环境准备代码


class CaseExecutor:
    def __init__(self, case_data: Dict[str, Any], elements: Dict[str, Any]):
        self.case_data = case_data
        self.elements = elements
        self.executed_fixtures: Set[str] = set()

    def execute_test_case(self, page, ui_helper) -> None:
        """执行测试用例
        Args:
            page: Playwright页面对象
            ui_helper: UI操作帮助类
        """
        import time
        import inspect
        from src.performance_monitor import performance_monitor

        # 记录测试开始时间
        test_start_time = time.time()

        # 获取测试用例名称 - 通过调用栈获取当前pytest测试函数名
        case_name = "未知测试用例"
        try:
            # 遍历调用栈，查找pytest测试函数
            for frame_info in inspect.stack():
                frame = frame_info.frame
                # 检查是否有pytest的request对象
                if 'request' in frame.f_locals:
                    request = frame.f_locals['request']
                    if hasattr(request, 'node') and hasattr(request.node, 'name'):
                        case_name = request.node.name
                        # 提取基础测试名称（去掉参数化部分）
                        if '[' in case_name:
                            case_name = case_name.split('[')[0]
                        break
        except Exception as e:
            log.debug(f"获取测试用例名称失败，使用默认名称: {e}")

        # 设置当前测试用例名称到断言管理器
        assertion_manager.set_current_test_case(case_name)
        log.info(f"开始执行测试用例: {case_name}")

        try:
            # 执行测试步骤
            step_executor = StepExecutor(page, ui_helper, self.elements)

            # 支持两种数据结构：直接的步骤列表或包含步骤的字典
            if isinstance(self.case_data, list):
                # 如果是列表，取第一个元素（兼容旧格式）
                if self.case_data and isinstance(self.case_data[0], dict):
                    steps = self.case_data[0].get("steps", [])
                else:
                    steps = []
            elif isinstance(self.case_data, dict):
                # 如果是字典，直接获取steps
                steps = self.case_data.get("steps", [])
            else:
                steps = []

            # 执行所有步骤
            for step in steps:
                step_executor.execute_step(step)

            log.info(f"测试用例 {case_name} 执行完成")

        except Exception as e:
            log.error(f"测试用例 {case_name} 执行失败: {e}")
            raise
        finally:
            # 记录测试执行时间
            test_end_time = time.time()
            test_duration = test_end_time - test_start_time
            performance_monitor.record_test_execution_time(test_duration)
            log.debug(f"测试用例 {case_name} 执行耗时: {test_duration:.2f}秒")

            # 输出当前测试用例的断言统计
            self._output_assertion_stats(case_name)

    def _output_assertion_stats(self, case_name: str):
        """输出断言统计信息"""
        stats = assertion_manager.get_stats()

        if stats.total_assertions > 0:
            log.info(f"=== 测试用例 {case_name} 断言统计 ===")
            log.info(f"总断言数: {stats.total_assertions}")
            log.info(f"通过断言: {stats.passed_assertions}")
            log.info(f"失败断言: {stats.failed_assertions}")
            log.info(f"  - 软断言失败: {stats.failed_soft_assertions}")
            log.info(f"  - 硬断言失败: {stats.failed_hard_assertions}")
            log.info(f"断言成功率: {stats.success_rate:.2f}%")

            # 如果有失败的断言，输出详细信息
            if stats.failed_assertions > 0:
                failed_assertions = assertion_manager.get_failed_assertions()
                log.warning(f"失败断言详情:")
                for i, assertion in enumerate(failed_assertions, 1):
                    log.warning(f"  {i}. [{assertion.assertion_type}断言] {assertion.step_description}")
                    log.warning(f"     错误: {assertion.error_message}")
                    if assertion.expected is not None:
                        log.warning(f"     期望: {assertion.expected}")
                    if assertion.actual is not None:
                        log.warning(f"     实际: {assertion.actual}")

            log.info("=" * 50)
        else:
            log.debug(f"测试用例 {case_name} 没有执行断言操作")
