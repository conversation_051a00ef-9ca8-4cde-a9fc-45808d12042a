import os
from pathlib import Path

import pytest
import yaml

from utils.logger import logger


class YamlTestFile(pytest.File):
    """YAML测试文件收集器，用于收集YAML文件中定义的测试用例"""

    def collect(self):
        """收集测试用例"""
        logger.info(f"开始收集测试文件: {self.fspath}")

        try:
            # 加载YAML文件内容
            with open(self.fspath, "r", encoding="utf-8") as f:
                file_content = f.read()
                logger.debug(f"文件内容: {file_content[:200]}...")
                data = yaml.safe_load(file_content)

            logger.debug(f"解析后的数据: {data}")

            # 确保YAML文件包含test_cases字段
            if not data:
                logger.warning(f"YAML文件 {self.fspath} 为空或格式错误")
                return

            if "test_cases" not in data:
                logger.warning(f"YAML文件 {self.fspath} 不包含test_cases字段，跳过")
                return

            # 遍历测试用例
            test_cases = data["test_cases"]
            logger.info(f"找到 {len(test_cases)} 个测试用例")

            for case in test_cases:
                if not isinstance(case, dict):
                    logger.warning(f"测试用例数据格式错误: {case}，应为字典类型")
                    continue

                # 获取测试用例名称
                case_name = case.get("name")
                if not case_name:
                    logger.warning(f"测试用例缺少name字段: {case}")
                    continue

                logger.info(f"创建测试项: {case_name}")

                # 创建测试项
                yield YamlTestItem.from_parent(
                    name=case_name, parent=self, case_data=case
                )
        except Exception as e:
            logger.error(f"解析YAML测试文件 {self.fspath} 时出错: {e}")
            import traceback

            logger.error(traceback.format_exc())
            return


class YamlTestItem(pytest.Item):
    """YAML测试项，表示一个测试用例"""

    def __init__(self, name, parent, case_data):
        super().__init__(name, parent)
        self.case_data = case_data
        self.test_dir = os.environ.get("TEST_DIR", "")
        logger.info(f"TEST_DIR环境变量: {self.test_dir}")

        # 获取测试用例所需的fixtures
        self.fixtures = case_data.get("fixtures", [])
        if isinstance(self.fixtures, str):
            self.fixtures = [self.fixtures]

        logger.info(f"测试用例 {name} 所需的fixtures: {self.fixtures}")

        # 添加默认的fixtures
        if "page" not in self.fixtures:
            self.fixtures.append("page")
        if "ui_helper" not in self.fixtures:
            self.fixtures.append("ui_helper")

        logger.info(f"添加默认fixtures后的fixtures列表: {self.fixtures}")

    def runtest(self):
        """执行测试用例"""
        from src.test_case_executor import CaseExecutor
        from src.case_utils import run_test_data, load_moules

        try:
            # 从中央逻辑加载所有测试数据、元素和变量
            test_data_collection = run_test_data()
            elements = test_data_collection.get("elements", {})

            # 查找并加载测试用例模块
            case_module = load_moules().get(self.case_data.get("name"), {})
            if not case_module:
                logger.warning(f"未在模块中找到名为 {self.case_data.get('name')} 的测试用例")

            # 将YAML文件中的数据覆盖到模块数据中
            case_module.update(self.case_data)
            self.case_data = case_module

            # 加载所有需要的fixtures
            fixtures = {}
            for fixture_name in self.fixtures:
                try:
                    fixtures[fixture_name] = self.getfixturevalue(fixture_name)
                except Exception as e:
                    logger.error(f"加载fixture {fixture_name} 失败: {e}")

            # 获取page和ui_helper对象
            page = fixtures.get("page")
            ui_helper = fixtures.get("ui_helper")

            if not page or not ui_helper:
                logger.error("缺少page或ui_helper fixture")
                pytest.fail("缺少page或ui_helper fixture")
                return

            # 执行测试用例
            executor = CaseExecutor(self.case_data, elements)
            executor.execute_test_case(page, ui_helper)

        except Exception as e:
            logger.error(f"执行测试用例 {self.name} 时出错: {e}")
            raise

    def reportinfo(self):
        """返回测试报告信息"""
        return self.fspath, 0, f"yaml_test: {self.name}"
