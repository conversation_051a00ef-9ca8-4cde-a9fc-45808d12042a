import logging

import httpx

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


class ApiClient:
    def __init__(self, base_url, session: httpx.Client = None):
        """
        初始化 API 客户端。

        Args:
            base_url (str): API 的基础 URL。
            session (httpx.Client, optional): 共享的 httpx.Client 会话。Defaults to None，创建一个新的session。
        """
        self.base_url = base_url
        if session:
            self.session = session
        else:
            self.session = httpx.Client(
                base_url=self.base_url
            )  # 使用base_url可以自动拼接url

    def _request(
        self, method, endpoint, data=None, headers=None, params=None, **kwargs
    ):
        """
        发送 HTTP 请求。

        Args:
            method (str): HTTP 方法 (GET, POST, PUT, DELETE 等)。
            endpoint (str): API 端点 (相对于 base_url)。
            data (dict, optional): 请求体数据 (JSON 格式)。 Defaults to None.
            headers (dict, optional): 请求头。 Defaults to None.
            params (dict, optional): URL 参数。 Defaults to None.
            **kwargs: 传递给 httpx.request 的其他参数。

        Returns:
            httpx.Response: 响应对象。

        Raises:
            httpx.HTTPStatusError: 如果请求状态码不是 2xx。
        """
        url = f"{self.base_url}{endpoint}"  # 此行代码可省略，因为session初始化时已经设置了base_url
        logging.info(
            f"Request: {method} {url} with data={data}, headers={headers}, params={params}"
        )  # 记录请求

        try:
            response = self.session.request(
                method, endpoint, json=data, headers=headers, params=params, **kwargs
            )  # 使用 json 参数会自动设置 Content-Type: application/json
            response.raise_for_status()  # 检查状态码，如果不是 2xx，则抛出异常
            logging.info(
                f"Response: Status Code = {response.status_code}, Content = {response.text}"
            )  # 记录响应
            return response
        except httpx.HTTPStatusError as e:
            logging.error(f"Request failed: {e}")
            raise  # 重新抛出异常，让调用者处理
        except httpx.RequestError as e:  # 处理连接错误等
            logging.error(f"Request failed due to connection error: {e}")
            raise

    def get(self, endpoint, params=None, headers=None, **kwargs):
        """发送 GET 请求。"""
        return self._request("GET", endpoint, params=params, headers=headers, **kwargs)

    def post(self, endpoint, data=None, headers=None, **kwargs):
        """发送 POST 请求。"""
        return self._request("POST", endpoint, data=data, headers=headers, **kwargs)

    def put(self, endpoint, data=None, headers=None, **kwargs):
        """发送 PUT 请求。"""
        return self._request("PUT", endpoint, data=data, headers=headers, **kwargs)

    def delete(self, endpoint, headers=None, **kwargs):
        """发送 DELETE 请求。"""
        return self._request("DELETE", endpoint, headers=headers, **kwargs)

    def get_json(self, endpoint, params=None, headers=None, **kwargs):
        """发送 GET 请求并返回 JSON 数据。"""
        response = self.get(endpoint, params, headers, **kwargs)
        return response.json()

    def post_json(self, endpoint, data=None, headers=None, **kwargs):
        """发送 POST 请求并返回 JSON 数据。"""
        response = self.post(endpoint, data, headers, **kwargs)
        return response.json()

    def close(self):
        """关闭会话。"""
        self.session.close()


# 示例用法：
if __name__ == "__main__":
    # 1. 创建一个共享的 session
    shared_session = httpx.Client(
        base_url="https://jsonplaceholder.typicode.com"
    )  # 替换为你的 API 基础 URL

    # 2. 创建 API 客户端实例，使用共享的 session
    api_client1 = ApiClient(
        base_url="https://jsonplaceholder.typicode.com", session=shared_session
    )
    api_client2 = ApiClient(
        base_url="https://jsonplaceholder.typicode.com", session=shared_session
    )

    # 3. 发送 GET 请求
    try:
        todos = api_client1.get_json("/todos/1")  # 获取 ID 为 1 的 todo
        print("GET Response (JSON):", todos)
        assert todos["userId"] == 1, "User ID should be 1"
    except httpx.HTTPStatusError as e:
        print(f"GET request failed: {e}")

    # 4. 发送 POST 请求
    try:
        new_todo = {"userId": 1, "title": "Learn Python httpx", "completed": False}
        created_todo = api_client2.post_json("/todos", data=new_todo)  # 创建新的 todo
        print("POST Response (JSON):", created_todo)
        assert created_todo["userId"] == 1, "User ID should be 1"
    except httpx.HTTPStatusError as e:
        print(f"POST request failed: {e}")

    # 5. 关闭共享 session
    shared_session.close()  # Important: Close the session
