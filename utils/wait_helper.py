"""
智能等待辅助工具，提供自适应超时和不同元素状态的等待能力
"""

import random
import time
from enum import Enum
from typing import Any, Callable, Optional

from playwright.sync_api import Page, TimeoutError as PlaywrightTimeoutError

from constants import DEFAULT_TIMEOUT
from utils.logger import logger


class ElementState(str, Enum):
    """元素状态枚举"""

    VISIBLE = "visible"
    HIDDEN = "hidden"
    ATTACHED = "attached"
    DETACHED = "detached"
    ENABLED = "enabled"
    DISABLED = "disabled"
    EDITABLE = "editable"
    CHECKED = "checked"
    UNCHECKED = "unchecked"
    STABLE = "stable"  # 元素位置稳定，不再移动


class WaitStrategy:
    """等待策略基类"""

    def __init__(self, page: Page):
        """
        初始化等待策略

        Args:
            page: Playwright页面对象
        """
        self.page = page

    def resolve_timeout(self, timeout: Optional[int] = None) -> int:
        """
        解析超时值，如果为None则使用默认超时

        Args:
            timeout: 超时时间(毫秒)，如果为None则使用默认超时

        Returns:
            实际使用的超时时间(毫秒)
        """
        return timeout or DEFAULT_TIMEOUT

    def wait_for_element(
        self,
        selector: str,
        state: ElementState = ElementState.VISIBLE,
        timeout: Optional[int] = None,
    ) -> bool:
        """
        等待元素达到指定状态

        Args:
            selector: 元素选择器
            state: 期望的元素状态
            timeout: 超时时间(毫秒)

        Returns:
            是否成功等待到元素达到指定状态
        """
        actual_timeout = self.resolve_timeout(timeout)

        try:
            if state == ElementState.VISIBLE:
                self.page.wait_for_selector(
                    selector, state="visible", timeout=actual_timeout
                )
            elif state == ElementState.HIDDEN:
                self.page.wait_for_selector(
                    selector, state="hidden", timeout=actual_timeout
                )
            elif state == ElementState.ATTACHED:
                self.page.wait_for_selector(
                    selector, state="attached", timeout=actual_timeout
                )
            elif state == ElementState.DETACHED:
                self.page.wait_for_selector(
                    selector, state="detached", timeout=actual_timeout
                )
            elif state == ElementState.ENABLED:
                self._wait_for_enabled(selector, actual_timeout)
            elif state == ElementState.DISABLED:
                self._wait_for_disabled(selector, actual_timeout)
            elif state == ElementState.EDITABLE:
                self._wait_for_editable(selector, actual_timeout)
            elif state == ElementState.CHECKED:
                self._wait_for_checked(selector, actual_timeout)
            elif state == ElementState.UNCHECKED:
                self._wait_for_unchecked(selector, actual_timeout)
            elif state == ElementState.STABLE:
                self._wait_for_element_stable(selector, actual_timeout)
            else:
                raise ValueError(f"不支持的元素状态: {state}")

            return True
        except PlaywrightTimeoutError:
            return False
        except Exception as e:
            logger.error(f"等待元素 '{selector}' 状态 '{state}' 时出错: {e}")
            return False

    def _wait_for_enabled(self, selector: str, timeout: int) -> None:
        """等待元素启用"""
        self._wait_for_condition(
            lambda: not self.page.locator(selector).is_disabled(),
            f"元素 '{selector}' 启用",
            timeout,
        )

    def _wait_for_disabled(self, selector: str, timeout: int) -> None:
        """等待元素禁用"""
        self._wait_for_condition(
            lambda: self.page.locator(selector).is_disabled(),
            f"元素 '{selector}' 禁用",
            timeout,
        )

    def _wait_for_editable(self, selector: str, timeout: int) -> None:
        """等待元素可编辑"""
        self._wait_for_condition(
            lambda: self.page.locator(selector).is_editable(),
            f"元素 '{selector}' 可编辑",
            timeout,
        )

    def _wait_for_checked(self, selector: str, timeout: int) -> None:
        """等待元素选中"""
        self._wait_for_condition(
            lambda: self.page.locator(selector).is_checked(),
            f"元素 '{selector}' 选中",
            timeout,
        )

    def _wait_for_unchecked(self, selector: str, timeout: int) -> None:
        """等待元素取消选中"""
        self._wait_for_condition(
            lambda: not self.page.locator(selector).is_checked(),
            f"元素 '{selector}' 取消选中",
            timeout,
        )

    def _wait_for_element_stable(self, selector: str, timeout: int) -> None:
        """等待元素位置稳定"""
        locator = self.page.locator(selector)

        # 确保元素存在
        locator.wait_for(state="attached", timeout=timeout)

        # 记录初始位置
        initial_box = None
        try:
            initial_box = locator.bounding_box()
        except Exception:
            # 元素可能还不可见
            time.sleep(0.1)
            initial_box = locator.bounding_box()

        if not initial_box:
            raise ValueError(f"无法获取元素 '{selector}' 的位置")

        def is_position_stable():
            try:
                current_box = locator.bounding_box()
                if not current_box:
                    return False

                # 检查位置是否变化
                position_diff = abs(current_box["x"] - initial_box["x"]) + abs(
                    current_box["y"] - initial_box["y"]
                )
                return position_diff < 2  # 允许2像素的误差
            except Exception:
                return False

        self._wait_for_condition(
            is_position_stable, f"元素 '{selector}' 位置稳定", timeout
        )

    def _wait_for_condition(
        self, condition: Callable[[], bool], description: str, timeout: int
    ) -> None:
        """
        等待条件满足

        Args:
            condition: 条件判断函数
            description: 条件描述
            timeout: 超时时间(毫秒)
        """
        start_time = time.time()
        end_time = start_time + (timeout / 1000)

        # 初始轮询间隔(毫秒)
        poll_interval = 50

        while time.time() < end_time:
            try:
                if condition():
                    return
            except Exception as e:
                logger.debug(f"检查条件 '{description}' 时出错: {e}")

            # 休眠一段时间
            time.sleep(poll_interval / 1000)

            # 自适应增加轮询间隔，最大500毫秒
            poll_interval = min(poll_interval * 1.5, 500)

        raise PlaywrightTimeoutError(f"等待条件 '{description}' 超时")


class AdaptiveWaitStrategy(WaitStrategy):
    """自适应等待策略，根据页面加载状态和元素特性动态调整超时时间"""

    def __init__(
        self,
        page: Page,
        base_multiplier: float = 1.0,
        min_timeout: int = 1000,
        max_timeout: int = 60000,
    ):
        """
        初始化自适应等待策略

        Args:
            page: Playwright页面对象
            base_multiplier: 基础超时时间乘数
            min_timeout: 最小超时时间(毫秒)
            max_timeout: 最大超时时间(毫秒)
        """
        super().__init__(page)
        self.base_multiplier = base_multiplier
        self.min_timeout = min_timeout
        self.max_timeout = max_timeout
        self.network_stats = {
            "request_count": 0,
            "pending_requests": 0,
            "slow_requests": 0,
        }
        self._setup_network_monitoring()

    def _setup_network_monitoring(self):
        """设置网络监控"""
        self.page.on("request", self._on_request)
        self.page.on("requestfinished", self._on_request_finished)
        self.page.on("requestfailed", self._on_request_failed)

    def _on_request(self, request):
        """请求开始事件处理"""
        self.network_stats["request_count"] += 1
        self.network_stats["pending_requests"] += 1

    def _on_request_finished(self, request):
        """请求完成事件处理"""
        self.network_stats["pending_requests"] = max(
            0, self.network_stats["pending_requests"] - 1
        )

    def _on_request_failed(self, request):
        """请求失败事件处理"""
        self.network_stats["pending_requests"] = max(
            0, self.network_stats["pending_requests"] - 1
        )

    def resolve_timeout(self, timeout: Optional[int] = None) -> int:
        """
        智能解析超时值，考虑网络状况和页面复杂度

        Args:
            timeout: 超时时间(毫秒)，如果为None则使用默认超时

        Returns:
            实际使用的超时时间(毫秒)
        """
        if timeout is not None:
            # 如果显式指定了超时时间，则使用指定值
            return timeout

        # 基础超时时间
        base_timeout = DEFAULT_TIMEOUT

        # 根据网络状况调整超时时间
        network_factor = 1.0
        if self.network_stats["pending_requests"] > 5:
            # 如果有大量挂起的请求，增加超时时间
            network_factor = 1.5

        # 根据页面复杂度调整超时时间
        try:
            element_count = len(self.page.query_selector_all("*"))
            complexity_factor = min(1 + (element_count / 1000), 2.0)  # 最多翻倍
        except Exception:
            complexity_factor = 1.0

        # 计算最终超时时间
        actual_timeout = int(
            base_timeout * self.base_multiplier * network_factor * complexity_factor
        )

        # 确保超时时间在合理范围内
        actual_timeout = max(self.min_timeout, min(actual_timeout, self.max_timeout))

        return actual_timeout

    def wait_with_retry(
        self,
        action: Callable[[], Any],
        max_retries: int = 3,
        retry_interval: int = 1000,
        description: str = "",
    ) -> Any:
        """
        带重试的等待操作

        Args:
            action: 要执行的操作函数
            max_retries: 最大重试次数
            retry_interval: 重试间隔(毫秒)
            description: 操作描述

        Returns:
            操作结果
        """
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                return action()
            except Exception as e:
                last_error = e
                if attempt < max_retries:
                    # 添加随机因子，避免多个重试同时发生
                    jitter = random.randint(0, min(1000, retry_interval))
                    wait_time = (retry_interval + jitter) / 1000

                    logger.warning(
                        f"操作 '{description}' 第 {attempt + 1} 次尝试失败，{wait_time:.2f}秒后重试: {e}"
                    )
                    time.sleep(wait_time)

        # 所有重试都失败了
        raise last_error
